import { Server as SocketIOServer, Socket } from 'socket.io';
import { verifyAccessToken } from '@/utils/auth';
import { query } from '@/config/database';
import { logger } from '@/utils/logger';
import { 
  WebSocketMessage, 
  TaskUpdateNotification, 
  UserStatusNotification 
} from '@/types/api';

// =====================================================
// WEBSOCKET SERVICE
// =====================================================

export class WebSocketService {
  private io: SocketIOServer;
  private connectedUsers: Map<string, { socketId: string; userId: string; departmentId: string }>;
  private userSockets: Map<string, string>; // userId -> socketId

  constructor(io: SocketIOServer) {
    this.io = io;
    this.connectedUsers = new Map();
    this.userSockets = new Map();
    this.setupEventHandlers();
  }

  private setupEventHandlers(): void {
    this.io.on('connection', (socket: Socket) => {
      logger.info('WebSocket connection established', { socketId: socket.id });

      // Handle authentication
      socket.on('authenticate', async (data) => {
        await this.handleAuthentication(socket, data);
      });

      // Handle task updates
      socket.on('task_update', async (data) => {
        await this.handleTaskUpdate(socket, data);
      });

      // Handle status updates
      socket.on('status_update', async (data) => {
        await this.handleStatusUpdate(socket, data);
      });

      // Handle join department room
      socket.on('join_department', async (data) => {
        await this.handleJoinDepartment(socket, data);
      });

      // Handle disconnection
      socket.on('disconnect', () => {
        this.handleDisconnection(socket);
      });

      // Handle ping for connection health
      socket.on('ping', () => {
        socket.emit('pong', { timestamp: new Date().toISOString() });
      });
    });
  }

  private async handleAuthentication(socket: Socket, data: any): Promise<void> {
    try {
      const { token } = data;

      if (!token) {
        socket.emit('authentication_error', { message: 'Token required' });
        return;
      }

      // Verify JWT token
      const payload = verifyAccessToken(token);
      if (!payload) {
        socket.emit('authentication_error', { message: 'Invalid token' });
        return;
      }

      // Get user details from database
      const result = await query(
        'SELECT id, name, department_id FROM users WHERE id = $1 AND is_active = true',
        [payload.userId]
      );

      if (result.rows.length === 0) {
        socket.emit('authentication_error', { message: 'User not found' });
        return;
      }

      const user = result.rows[0];

      // Store user connection
      this.connectedUsers.set(socket.id, {
        socketId: socket.id,
        userId: user.id,
        departmentId: user.department_id
      });

      this.userSockets.set(user.id, socket.id);

      // Join user-specific room
      socket.join(`user:${user.id}`);
      
      // Join department room
      socket.join(`department:${user.department_id}`);

      logger.info('User authenticated via WebSocket', {
        socketId: socket.id,
        userId: user.id,
        departmentId: user.department_id
      });

      socket.emit('authenticated', {
        success: true,
        user: {
          id: user.id,
          name: user.name,
          department_id: user.department_id
        }
      });

      // Notify department about user coming online
      this.broadcastToRoom(`department:${user.department_id}`, 'user_online', {
        userId: user.id,
        userName: user.name,
        timestamp: new Date().toISOString()
      }, socket.id);

    } catch (error) {
      logger.error('WebSocket authentication error', error);
      socket.emit('authentication_error', { message: 'Authentication failed' });
    }
  }

  private async handleTaskUpdate(socket: Socket, data: any): Promise<void> {
    try {
      const userConnection = this.connectedUsers.get(socket.id);
      if (!userConnection) {
        socket.emit('error', { message: 'Not authenticated' });
        return;
      }

      // Get user name for notification
      const userResult = await query(
        'SELECT name FROM users WHERE id = $1',
        [userConnection.userId]
      );

      if (userResult.rows.length === 0) {
        socket.emit('error', { message: 'User not found' });
        return;
      }

      const userName = userResult.rows[0].name;

      // Create task update notification
      const notification: TaskUpdateNotification = {
        type: 'task_update',
        data: {
          user_id: userConnection.userId,
          user_name: userName,
          department_id: userConnection.departmentId,
          task_description: data.task_description,
          status: data.status,
          priority: data.priority,
          updated_at: new Date().toISOString()
        }
      };

      // Broadcast to department members
      this.broadcastToRoom(
        `department:${userConnection.departmentId}`,
        'task_updated',
        notification,
        socket.id
      );

      // Broadcast to admins
      this.broadcastToRole('admin', 'task_updated', notification);

      logger.info('Task update broadcasted', {
        userId: userConnection.userId,
        departmentId: userConnection.departmentId,
        socketId: socket.id
      });

    } catch (error) {
      logger.error('WebSocket task update error', error);
      socket.emit('error', { message: 'Failed to process task update' });
    }
  }

  private async handleStatusUpdate(socket: Socket, data: any): Promise<void> {
    try {
      const userConnection = this.connectedUsers.get(socket.id);
      if (!userConnection) {
        socket.emit('error', { message: 'Not authenticated' });
        return;
      }

      // Get user name
      const userResult = await query(
        'SELECT name FROM users WHERE id = $1',
        [userConnection.userId]
      );

      if (userResult.rows.length === 0) {
        socket.emit('error', { message: 'User not found' });
        return;
      }

      const userName = userResult.rows[0].name;

      // Create status update notification
      const notification: UserStatusNotification = {
        type: 'user_status',
        data: {
          user_id: userConnection.userId,
          user_name: userName,
          status: data.status,
          last_seen: new Date().toISOString()
        }
      };

      // Broadcast to department members
      this.broadcastToRoom(
        `department:${userConnection.departmentId}`,
        'user_status_updated',
        notification,
        socket.id
      );

      logger.info('Status update broadcasted', {
        userId: userConnection.userId,
        status: data.status,
        socketId: socket.id
      });

    } catch (error) {
      logger.error('WebSocket status update error', error);
      socket.emit('error', { message: 'Failed to process status update' });
    }
  }

  private async handleJoinDepartment(socket: Socket, data: any): Promise<void> {
    try {
      const userConnection = this.connectedUsers.get(socket.id);
      if (!userConnection) {
        socket.emit('error', { message: 'Not authenticated' });
        return;
      }

      const { departmentId } = data;

      // Verify user has access to this department
      if (userConnection.departmentId !== departmentId) {
        // Check if user is admin or manager
        const userResult = await query(
          'SELECT role FROM users WHERE id = $1',
          [userConnection.userId]
        );

        if (userResult.rows.length === 0 || 
            !['admin', 'manager'].includes(userResult.rows[0].role)) {
          socket.emit('error', { message: 'Access denied to department' });
          return;
        }
      }

      // Join department room
      socket.join(`department:${departmentId}`);

      socket.emit('department_joined', {
        departmentId,
        timestamp: new Date().toISOString()
      });

      logger.info('User joined department room', {
        userId: userConnection.userId,
        departmentId,
        socketId: socket.id
      });

    } catch (error) {
      logger.error('WebSocket join department error', error);
      socket.emit('error', { message: 'Failed to join department' });
    }
  }

  private handleDisconnection(socket: Socket): void {
    const userConnection = this.connectedUsers.get(socket.id);
    
    if (userConnection) {
      // Remove from maps
      this.connectedUsers.delete(socket.id);
      this.userSockets.delete(userConnection.userId);

      // Notify department about user going offline
      this.broadcastToRoom(`department:${userConnection.departmentId}`, 'user_offline', {
        userId: userConnection.userId,
        timestamp: new Date().toISOString()
      });

      logger.info('WebSocket connection closed', {
        socketId: socket.id,
        userId: userConnection.userId
      });
    } else {
      logger.info('WebSocket connection closed (unauthenticated)', {
        socketId: socket.id
      });
    }
  }

  // =====================================================
  // PUBLIC METHODS FOR EXTERNAL USE
  // =====================================================

  public broadcastTaskUpdate(userId: string, taskData: any): void {
    const socketId = this.userSockets.get(userId);
    if (socketId) {
      const userConnection = this.connectedUsers.get(socketId);
      if (userConnection) {
        this.broadcastToRoom(
          `department:${userConnection.departmentId}`,
          'task_updated',
          {
            type: 'task_update',
            data: {
              user_id: userId,
              ...taskData,
              updated_at: new Date().toISOString()
            }
          }
        );
      }
    }
  }

  public broadcastStatusUpdate(userId: string, status: string): void {
    const socketId = this.userSockets.get(userId);
    if (socketId) {
      const userConnection = this.connectedUsers.get(socketId);
      if (userConnection) {
        this.broadcastToRoom(
          `department:${userConnection.departmentId}`,
          'user_status_updated',
          {
            type: 'user_status',
            data: {
              user_id: userId,
              status,
              last_seen: new Date().toISOString()
            }
          }
        );
      }
    }
  }

  public getConnectedUsers(): Array<{ userId: string; departmentId: string }> {
    return Array.from(this.connectedUsers.values()).map(conn => ({
      userId: conn.userId,
      departmentId: conn.departmentId
    }));
  }

  public isUserConnected(userId: string): boolean {
    return this.userSockets.has(userId);
  }

  // =====================================================
  // PRIVATE HELPER METHODS
  // =====================================================

  private broadcastToRoom(room: string, event: string, data: any, excludeSocketId?: string): void {
    if (excludeSocketId) {
      this.io.to(room).except(excludeSocketId).emit(event, data);
    } else {
      this.io.to(room).emit(event, data);
    }
  }

  private async broadcastToRole(role: string, event: string, data: any): Promise<void> {
    try {
      const result = await query(
        'SELECT id FROM users WHERE role = $1 AND is_active = true',
        [role]
      );

      const userIds = result.rows.map(row => row.id);
      
      userIds.forEach(userId => {
        const socketId = this.userSockets.get(userId);
        if (socketId) {
          this.io.to(socketId).emit(event, data);
        }
      });
    } catch (error) {
      logger.error('Error broadcasting to role', { role, error });
    }
  }
}

// Export singleton instance
let websocketService: WebSocketService | null = null;

export const initializeWebSocketService = (io: SocketIOServer): WebSocketService => {
  websocketService = new WebSocketService(io);
  return websocketService;
};

export const getWebSocketService = (): WebSocketService | null => {
  return websocketService;
};
