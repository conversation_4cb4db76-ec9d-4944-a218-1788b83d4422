/* eslint-disable react-refresh/only-export-components */
import React, { createContext, useContext, useState, useEffect, type ReactNode } from 'react';
import { API_BASE_URL } from '../config/api';

interface User {
  id: string;
  name: string;
  email: string;
  departmentId: string;
}

interface AuthError {
  field?: string;
  message: string;
  code?: string;
}

interface AuthResult {
  success: boolean;
  errors?: AuthError[];
}

interface AuthContextType {
  user: User | null;
  login: (email: string, password: string) => Promise<AuthResult>;
  signup: (name: string, email: string, password: string, departmentId: string) => Promise<AuthResult>;
  logout: () => void;
  isLoading: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Check for stored user session
    const storedUser = localStorage.getItem('user');
    const storedToken = localStorage.getItem('token');

    // Only restore session if both user and token exist
    if (storedUser && storedToken) {
      setUser(JSON.parse(storedUser));
    } else {
      // If either is missing, clear both to ensure clean state
      localStorage.removeItem('user');
      localStorage.removeItem('token');
    }

    setIsLoading(false);
  }, []);

  const login = async (email: string, password: string): Promise<AuthResult> => {
    setIsLoading(true);
    try {
      const response = await fetch(`${API_BASE_URL}/api/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, password }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        console.error('Login failed:', errorData);
        setIsLoading(false);
        return {
          success: false,
          errors: errorData.errors || [{ message: errorData.message || 'Login failed' }]
        };
      }

      const data = await response.json();

      if (!data.success || !data.data) {
        console.error('Invalid response format:', data);
        setIsLoading(false);
        return {
          success: false,
          errors: [{ message: 'Invalid response from server' }]
        };
      }

      const authenticatedUser: User = {
        id: data.data.user.id,
        name: data.data.user.name,
        email: data.data.user.email,
        departmentId: data.data.user.department_id
      };

      setUser(authenticatedUser);
      localStorage.setItem('user', JSON.stringify(authenticatedUser));
      localStorage.setItem('token', data.data.token);
      setIsLoading(false);
      return { success: true };
    } catch (error) {
      console.error('Login error:', error);
      setIsLoading(false);
      return {
        success: false,
        errors: [{ message: 'An error occurred. Please try again.' }]
      };
    }
  };

  const signup = async (name: string, email: string, password: string, departmentId: string): Promise<AuthResult> => {
    setIsLoading(true);
    try {
      const response = await fetch(`${API_BASE_URL}/api/auth/signup`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name,
          email,
          password,
          department_id: departmentId
        }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        console.error('Signup failed:', errorData);
        setIsLoading(false);
        return {
          success: false,
          errors: errorData.errors || [{ message: errorData.message || 'Signup failed' }]
        };
      }

      const data = await response.json();

      if (!data.success || !data.data) {
        console.error('Invalid response format:', data);
        setIsLoading(false);
        return {
          success: false,
          errors: [{ message: 'Invalid response from server' }]
        };
      }

      const newUser: User = {
        id: data.data.user.id,
        name: data.data.user.name,
        email: data.data.user.email,
        departmentId: data.data.user.department_id
      };

      setUser(newUser);
      localStorage.setItem('user', JSON.stringify(newUser));
      localStorage.setItem('token', data.data.token);
      setIsLoading(false);
      return { success: true };
    } catch (error) {
      console.error('Signup error:', error);
      setIsLoading(false);
      return {
        success: false,
        errors: [{ message: 'An error occurred. Please try again.' }]
      };
    }
  };

  const logout = () => {
    setUser(null);
    localStorage.removeItem('user');
    localStorage.removeItem('token');
  };

  const value: AuthContextType = {
    user,
    login,
    signup,
    logout,
    isLoading
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
