import { Router } from 'express';
import { getDatabaseDiagnostics, testTaskEndpoints } from '@/controllers/diagnosticsController';
import { authenticateToken, requireAdmin } from '@/middleware/auth';

const router = Router();

// =====================================================
// DIAGNOSTICS ROUTES
// =====================================================

/**
 * @route   GET /api/diagnostics/database
 * @desc    Get database diagnostics information
 * @access  Private (Admin only)
 * @returns Database table status, constraints, indexes, and sample data
 */
router.get('/database',
  authenticateToken,
  requireAdmin,
  getDatabaseDiagnostics
);

/**
 * @route   GET /api/diagnostics/test-endpoints
 * @desc    Test critical endpoints for the current user
 * @access  Private
 * @returns Test results for task and department endpoints
 */
router.get('/test-endpoints',
  authenticateToken,
  testTaskEndpoints
);

export default router;
