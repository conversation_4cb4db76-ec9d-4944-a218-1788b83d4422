-- Initial Data Migration
-- This file contains sample data for development and testing

-- =====================================================
-- SAMPLE DEPARTMENTS
-- =====================================================

INSERT INTO departments (id, name, description) VALUES
('550e8400-e29b-41d4-a716-************', 'Engineering', 'Software development and technical operations'),
('550e8400-e29b-41d4-a716-************', 'Design', 'UI/UX design and creative services'),
('550e8400-e29b-41d4-a716-************', 'Marketing', 'Digital marketing and brand management'),
('550e8400-e29b-41d4-a716-************', 'Sales', 'Business development and client relations'),
('550e8400-e29b-41d4-a716-************', 'HR', 'Human resources and talent management'),
('550e8400-e29b-41d4-a716-************', 'Finance', 'Financial planning and accounting');

-- =====================================================
-- SAMPLE PROJECTS
-- =====================================================

INSERT INTO projects (id, name, description, client_name, department_id, start_date, end_date, status, priority) VALUES
('650e8400-e29b-41d4-a716-************', 'Employee Dashboard', 'Internal employee task tracking system', 'Internal', '550e8400-e29b-41d4-a716-************', '2024-01-01', '2024-03-31', 'active', 'high'),
('650e8400-e29b-41d4-a716-************', 'Client Portal', 'Customer-facing portal for project management', 'TechCorp Inc', '550e8400-e29b-41d4-a716-************', '2024-02-01', '2024-05-31', 'active', 'urgent'),
('650e8400-e29b-41d4-a716-************', 'Brand Redesign', 'Complete brand identity overhaul', 'StartupXYZ', '550e8400-e29b-41d4-a716-************', '2024-01-15', '2024-04-15', 'active', 'medium'),
('650e8400-e29b-41d4-a716-************', 'Marketing Campaign', 'Q1 digital marketing campaign', 'RetailCorp', '550e8400-e29b-41d4-a716-************', '2024-01-01', '2024-03-31', 'active', 'high'),
('650e8400-e29b-41d4-a716-************', 'Sales Automation', 'CRM integration and automation', 'Internal', '550e8400-e29b-41d4-a716-************', '2024-02-15', '2024-06-15', 'active', 'medium');

-- =====================================================
-- SAMPLE USERS
-- =====================================================

-- Note: Password is 'password123' hashed with bcrypt
-- In production, use proper password hashing
INSERT INTO users (id, email, name, password_hash, department_id, role, hire_date, timezone, skills) VALUES
('750e8400-e29b-41d4-a716-************', '<EMAIL>', 'John Doe', '$2b$10$rOzJqQZQZQZQZQZQZQZQZOzJqQZQZQZQZQZQZQZQZOzJqQZQZQZQZQ', '550e8400-e29b-41d4-a716-************', 'employee', '2023-01-15', 'America/New_York', '["React", "TypeScript", "Node.js", "PostgreSQL"]'),
('750e8400-e29b-41d4-a716-************', '<EMAIL>', 'Jane Smith', '$2b$10$rOzJqQZQZQZQZQZQZQZQZOzJqQZQZQZQZQZQZQZQZOzJqQZQZQZQZQ', '550e8400-e29b-41d4-a716-************', 'manager', '2022-03-01', 'America/New_York', '["Python", "Django", "AWS", "Docker", "Kubernetes"]'),
('************************************', '<EMAIL>', 'Mike Johnson', '$2b$10$rOzJqQZQZQZQZQZQZQZQZOzJqQZQZQZQZQZQZQZQZOzJqQZQZQZQZQ', '550e8400-e29b-41d4-a716-************', 'employee', '2023-06-01', 'America/Los_Angeles', '["Figma", "Adobe Creative Suite", "UI/UX Design", "Prototyping"]'),
('750e8400-e29b-41d4-a716-************', '<EMAIL>', 'Sarah Wilson', '$2b$10$rOzJqQZQZQZQZQZQZQZQZOzJqQZQZQZQZQZQZQZQZOzJqQZQZQZQZQ', '550e8400-e29b-41d4-a716-************', 'manager', '2021-09-15', 'America/Los_Angeles', '["Design Systems", "User Research", "Figma", "Sketch"]'),
('750e8400-e29b-41d4-a716-************', '<EMAIL>', 'Alex Brown', '$2b$10$rOzJqQZQZQZQZQZQZQZQZOzJqQZQZQZQZQZQZQZQZOzJqQZQZQZQZQ', '550e8400-e29b-41d4-a716-************', 'employee', '2023-02-01', 'America/Chicago', '["Digital Marketing", "SEO", "Google Analytics", "Social Media"]'),
('750e8400-e29b-41d4-a716-************', '<EMAIL>', 'Lisa Davis', '$2b$10$rOzJqQZQZQZQZQZQZQZQZOzJqQZQZQZQZQZQZQZQZOzJqQZQZQZQZQ', '550e8400-e29b-41d4-a716-************', 'employee', '2023-04-01', 'America/New_York', '["Sales", "CRM", "Lead Generation", "Customer Relations"]'),
('750e8400-e29b-41d4-a716-************', '<EMAIL>', 'Tom Garcia', '$2b$10$rOzJqQZQZQZQZQZQZQZQZOzJqQZQZQZQZQZQZQZQZOzJqQZQZQZQZQ', '550e8400-e29b-41d4-a716-************', 'manager', '2022-01-01', 'America/New_York', '["HR Management", "Recruitment", "Employee Relations", "Training"]'),
('750e8400-e29b-41d4-a716-************', '<EMAIL>', 'Emma Martinez', '$2b$10$rOzJqQZQZQZQZQZQZQZQZOzJqQZQZQZQZQZQZQZQZOzJqQZQZQZQZQ', '550e8400-e29b-41d4-a716-************', 'employee', '2023-03-15', 'America/Denver', '["Financial Analysis", "Accounting", "Excel", "QuickBooks"]');

-- Update department managers
UPDATE departments SET manager_id = '750e8400-e29b-41d4-a716-************' WHERE id = '550e8400-e29b-41d4-a716-************'; -- Engineering
UPDATE departments SET manager_id = '750e8400-e29b-41d4-a716-************' WHERE id = '550e8400-e29b-41d4-a716-************'; -- Design
UPDATE departments SET manager_id = '750e8400-e29b-41d4-a716-************' WHERE id = '550e8400-e29b-41d4-a716-************'; -- HR

-- =====================================================
-- SAMPLE TASK TAGS
-- =====================================================

INSERT INTO task_tags (id, name, color, description) VALUES
('850e8400-e29b-41d4-a716-************', 'frontend', '#3B82F6', 'Frontend development tasks'),
('850e8400-e29b-41d4-a716-************', 'backend', '#10B981', 'Backend development tasks'),
('850e8400-e29b-41d4-a716-************', 'database', '#8B5CF6', 'Database related work'),
('850e8400-e29b-41d4-a716-************', 'urgent', '#EF4444', 'Urgent priority tasks'),
('850e8400-e29b-41d4-a716-************', 'research', '#F59E0B', 'Research and investigation'),
('850e8400-e29b-41d4-a716-************', 'client-work', '#06B6D4', 'Client-facing work'),
('850e8400-e29b-41d4-a716-************', 'internal', '#6B7280', 'Internal company work'),
('850e8400-e29b-41d4-a716-************', 'design-system', '#EC4899', 'Design system work'),
('850e8400-e29b-41d4-a716-446655440009', 'analytics', '#84CC16', 'Analytics and reporting'),
('850e8400-e29b-41d4-a716-446655440010', 'optimization', '#F97316', 'Performance optimization');

-- =====================================================
-- SAMPLE CURRENT TASK UPDATES
-- =====================================================

INSERT INTO task_updates (id, user_id, task_description, status, priority, category, estimated_duration_minutes, progress_percentage, project_id, expected_completion_date, blocking_issues) VALUES
('950e8400-e29b-41d4-a716-************', '750e8400-e29b-41d4-a716-************', 'Implementing user authentication system with JWT tokens and refresh token rotation', 'active', 'high', 'development', 480, 75, '650e8400-e29b-41d4-a716-************', '2024-02-15', NULL),
('950e8400-e29b-41d4-a716-************', '750e8400-e29b-41d4-a716-************', 'Code review for authentication module and database schema optimization', 'active', 'medium', 'review', 120, 50, '650e8400-e29b-41d4-a716-************', '2024-02-10', NULL),
('950e8400-e29b-41d4-a716-************', '************************************', 'Creating wireframes for client portal dashboard with responsive design considerations', 'active', 'high', 'development', 360, 60, '650e8400-e29b-41d4-a716-************', '2024-02-20', NULL),
('950e8400-e29b-41d4-a716-************', '750e8400-e29b-41d4-a716-************', 'Design system documentation and component library updates', 'idle', 'medium', 'documentation', 240, 30, '650e8400-e29b-41d4-a716-************', '2024-02-25', 'Waiting for stakeholder feedback on color palette'),
('950e8400-e29b-41d4-a716-************', '750e8400-e29b-41d4-a716-************', 'Q1 marketing campaign performance analysis and optimization recommendations', 'active', 'urgent', 'research', 180, 85, '650e8400-e29b-41d4-a716-************', '2024-02-12', NULL),
('950e8400-e29b-41d4-a716-************', '750e8400-e29b-41d4-a716-************', 'Lead qualification and follow-up calls for enterprise prospects', 'active', 'high', 'meeting', 300, 40, '650e8400-e29b-41d4-a716-************', '2024-02-18', NULL),
('950e8400-e29b-41d4-a716-************', '750e8400-e29b-41d4-a716-************', 'Employee onboarding process improvement and documentation update', 'offline', 'low', 'planning', 420, 20, NULL, '2024-03-01', NULL),
('950e8400-e29b-41d4-a716-************', '750e8400-e29b-41d4-a716-************', 'Monthly financial reports preparation and budget variance analysis', 'active', 'medium', 'documentation', 240, 90, NULL, '2024-02-14', NULL);

-- =====================================================
-- SAMPLE TASK-TAG RELATIONSHIPS
-- =====================================================

INSERT INTO task_update_tags (task_update_id, task_tag_id) VALUES
('950e8400-e29b-41d4-a716-************', '850e8400-e29b-41d4-a716-************'), -- backend
('950e8400-e29b-41d4-a716-************', '850e8400-e29b-41d4-a716-************'), -- database
('950e8400-e29b-41d4-a716-************', '850e8400-e29b-41d4-a716-************'), -- internal
('950e8400-e29b-41d4-a716-************', '850e8400-e29b-41d4-a716-************'), -- frontend
('950e8400-e29b-41d4-a716-************', '850e8400-e29b-41d4-a716-************'), -- client-work
('950e8400-e29b-41d4-a716-************', '850e8400-e29b-41d4-a716-************'), -- design-system
('950e8400-e29b-41d4-a716-************', '850e8400-e29b-41d4-a716-446655440009'), -- analytics
('950e8400-e29b-41d4-a716-************', '850e8400-e29b-41d4-a716-************'), -- urgent
('950e8400-e29b-41d4-a716-************', '850e8400-e29b-41d4-a716-************'), -- client-work
('950e8400-e29b-41d4-a716-************', '850e8400-e29b-41d4-a716-************'); -- internal
