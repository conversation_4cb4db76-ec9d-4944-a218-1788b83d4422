-- Migration: Update task categories to new business-specific categories
-- Date: 2025-01-30
-- Description: Replace generic task categories with specific business categories

BEGIN;

-- First, update existing data to map old categories to new ones
-- This is a best-effort mapping - you may need to adjust based on your data

-- Map development-related categories to project-delivery
UPDATE task_updates 
SET category = 'project-delivery' 
WHERE category IN ('development', 'bug-fix', 'testing');

-- Map documentation and review to quality-checking
UPDATE task_updates 
SET category = 'quality-checking' 
WHERE category IN ('documentation', 'review');

-- Map research and planning to question-creation
UPDATE task_updates 
SET category = 'question-creation' 
WHERE category IN ('research', 'planning');

-- Map meeting to uploading (you may want to adjust this mapping)
UPDATE task_updates 
SET category = 'uploading' 
WHERE category = 'meeting';

-- Update task_history table as well
UPDATE task_history 
SET category = 'project-delivery' 
WHERE category IN ('development', 'bug-fix', 'testing');

UPDATE task_history 
SET category = 'quality-checking' 
WHERE category IN ('documentation', 'review');

UPDATE task_history 
SET category = 'question-creation' 
WHERE category IN ('research', 'planning');

UPDATE task_history 
SET category = 'uploading' 
WHERE category = 'meeting';

-- Drop the old constraint
ALTER TABLE task_updates 
DROP CONSTRAINT IF EXISTS task_updates_category_check;

-- Add the new constraint with updated categories
ALTER TABLE task_updates 
ADD CONSTRAINT task_updates_category_check 
CHECK (category IN ('question-creation', 'project-delivery', 'uploading', 'quality-checking'));

-- Update task_history table constraint if it exists
ALTER TABLE task_history 
DROP CONSTRAINT IF EXISTS task_history_category_check;

ALTER TABLE task_history 
ADD CONSTRAINT task_history_category_check 
CHECK (category IN ('question-creation', 'project-delivery', 'uploading', 'quality-checking'));

-- Update task_performance_metrics table if it has category constraints
-- Note: This table might have existing data that needs to be updated
UPDATE task_performance_metrics 
SET category = 'project-delivery' 
WHERE category IN ('development', 'bug-fix', 'testing');

UPDATE task_performance_metrics 
SET category = 'quality-checking' 
WHERE category IN ('documentation', 'review');

UPDATE task_performance_metrics 
SET category = 'question-creation' 
WHERE category IN ('research', 'planning');

UPDATE task_performance_metrics 
SET category = 'uploading' 
WHERE category = 'meeting';

COMMIT;

-- Verify the migration
SELECT 
    category, 
    COUNT(*) as count 
FROM task_updates 
GROUP BY category 
ORDER BY category;

SELECT 
    category, 
    COUNT(*) as count 
FROM task_history 
GROUP BY category 
ORDER BY category;
