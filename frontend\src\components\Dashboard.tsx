import React, { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { <PERSON><PERSON>, Card, CardContent, CardHeader, CardTitle, CardDescription, Badge, EmptyState } from './ui';
import { UserSearch } from './UserProfilePage';
import { API_BASE_URL } from '../config/api';

interface Department {
  id: string;
  name: string;
  description: string;
  total_employees: number;
  active_employees: number;
  idle_employees: number;
  offline_employees: number;
  activity_percentage: number;
  urgent_tasks: number;
}

interface DashboardProps {
  onDepartmentSelect: (departmentId: string) => void;
  onViewProfile?: (userId: string) => void;
}

const Dashboard: React.FC<DashboardProps> = ({ onDepartmentSelect, onViewProfile }) => {
  const { user, logout } = useAuth();

  // TODO: Replace with real API call to fetch departments
  const [departments, setDepartments] = useState<Department[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchDepartments = async () => {
      try {
        setIsLoading(true);
        setError(null);

        const token = localStorage.getItem('token');
        if (!token) {
          setError('Authentication required');
          return;
        }

        console.log('Fetching departments from:', `${API_BASE_URL}/api/departments`);

        const response = await fetch(`${API_BASE_URL}/api/departments`, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        });

        console.log('Departments response status:', response.status);

        if (!response.ok) {
          const errorText = await response.text();
          console.error('Departments error response:', errorText);

          // If unauthorized, clear the token and redirect to login
          if (response.status === 401) {
            console.error('Authentication failed - clearing stored token');
            localStorage.removeItem('token');
            localStorage.removeItem('user');
            // Optionally reload the page to trigger auth redirect
            window.location.reload();
            return;
          }

          throw new Error(`HTTP error! status: ${response.status} - ${errorText}`);
        }

        const data = await response.json();
        console.log('Departments response data:', data);

        if (data.success && data.data && data.data.departments) {
          console.log('Setting departments:', data.data.departments);
          setDepartments(data.data.departments);
        } else {
          console.error('Invalid response format:', data);
          setError('Invalid response from server');
        }
      } catch (err) {
        setError('Failed to load departments');
        console.error('Error fetching departments:', err);
      } finally {
        setIsLoading(false);
      }
    };

    fetchDepartments();
  }, []);

  return (
    <div className="min-h-screen bg-neutral-50 pb-32">
      {/* Modern Header */}
      <header className="bg-white border-b border-neutral-200 sticky top-0 z-30 backdrop-blur-md bg-white/95">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center space-x-4">
              <div className="w-10 h-10 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-xl flex items-center justify-center">
                <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
              </div>
              <div>
                <h1 className="text-2xl font-bold text-neutral-900">Employee Dashboard</h1>
                <p className="text-sm text-neutral-600">Welcome back, {user?.name}</p>
              </div>
            </div>
            <Button
              variant="error"
              size="sm"
              onClick={logout}
              icon={
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                </svg>
              }
            >
              Sign Out
            </Button>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
        <div className="space-y-8">
          {/* Page Header */}
          <div className="text-center lg:text-left">
            <h2 className="text-3xl font-bold text-neutral-900 mb-3">Departments</h2>
            <p className="text-lg text-neutral-600 max-w-2xl">
              Select a department to view employee status and manage team activities
            </p>
          </div>

          {/* Employee Search */}
          {onViewProfile && (
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Find Employee Profile</h3>
              <UserSearch
                onUserSelect={onViewProfile}
                className="max-w-md"
              />
            </div>
          )}

          {/* Department Grid */}
          {isLoading ? (
            <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
              {[...Array(4)].map((_, i) => (
                <Card key={i} className="animate-pulse">
                  <CardHeader className="pb-4">
                    <div className="flex items-center space-x-4">
                      <div className="w-12 h-12 bg-neutral-200 rounded-xl"></div>
                      <div className="flex-1 space-y-2">
                        <div className="h-4 bg-neutral-200 rounded w-3/4"></div>
                        <div className="h-3 bg-neutral-200 rounded w-1/2"></div>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className="pt-0 space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div className="h-16 bg-neutral-200 rounded-lg"></div>
                      <div className="h-16 bg-neutral-200 rounded-lg"></div>
                    </div>
                    <div className="h-4 bg-neutral-200 rounded"></div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : error ? (
            <Card className="text-center py-12">
              <CardContent>
                <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <h3 className="text-lg font-semibold text-neutral-900 mb-2">Failed to Load Departments</h3>
                <p className="text-neutral-600 mb-4">{error}</p>
                <Button onClick={() => window.location.reload()}>Try Again</Button>
              </CardContent>
            </Card>
          ) : departments.length === 0 ? (
            <EmptyState
              icon={
                <svg className="w-12 h-12 text-neutral-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2-2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                </svg>
              }
              title="No Departments Found"
              description="No departments have been set up yet. Contact your administrator to create departments and add employees."
              action={{
                label: "Refresh",
                onClick: () => window.location.reload(),
                variant: "secondary"
              }}
            />
          ) : (
            <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
              {departments.map((department) => (
                <Card
                  key={department.id}
                  variant="interactive"
                  padding="none"
                  onClick={() => onDepartmentSelect(department.id)}
                  className="group hover-lift"
                >
                  <CardHeader className="pb-4">
                    <div className="flex items-center space-x-4">
                      <div className="w-12 h-12 bg-gradient-to-br from-primary-100 to-primary-200 rounded-xl flex items-center justify-center group-hover:from-primary-200 group-hover:to-primary-300 transition-all duration-300">
                        <svg
                          className="w-6 h-6 text-primary-600"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"
                          />
                        </svg>
                      </div>
                      <div className="flex-1 min-w-0">
                        <CardTitle className="text-lg truncate">{department.name}</CardTitle>
                        <CardDescription className="text-sm mt-1 line-clamp-2">
                          {department.description}
                        </CardDescription>
                      </div>
                    </div>
                  </CardHeader>

                  <CardContent className="pt-0 space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div className="text-center p-3 bg-neutral-50 rounded-lg">
                        <div className="text-2xl font-bold text-neutral-900">{department.total_employees}</div>
                        <div className="text-xs text-neutral-600 font-medium">Total</div>
                      </div>
                      <div className="text-center p-3 bg-success-50 rounded-lg">
                        <div className="text-2xl font-bold text-success-600">{department.active_employees}</div>
                        <div className="text-xs text-success-700 font-medium">Active</div>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-neutral-600">Activity Rate</span>
                        <Badge variant="success" size="sm">
                          {department.activity_percentage}%
                        </Badge>
                      </div>
                      <div className="w-full bg-neutral-200 rounded-full h-2 overflow-hidden">
                        <div
                          className="bg-gradient-to-r from-success-500 to-success-600 h-2 rounded-full transition-all duration-500 ease-out"
                          style={{
                            width: `${department.activity_percentage}%`
                          }}
                        />
                      </div>
                    </div>

                    <div className="flex items-center justify-center pt-2 text-sm text-primary-600 font-medium group-hover:text-primary-700 transition-colors">
                      <span>View Employees</span>
                      <svg
                        className="ml-1 w-4 h-4 group-hover:translate-x-1 transition-transform"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M9 5l7 7-7 7"
                        />
                      </svg>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}

          {/* Quick Stats */}
          {!isLoading && !error && departments.length > 0 && (
            <div className="space-y-6">
              <div className="text-center lg:text-left">
                <h3 className="text-2xl font-bold text-neutral-900 mb-2">Quick Overview</h3>
                <p className="text-neutral-600">Real-time insights across all departments</p>
              </div>

              <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
                <Card className="text-center">
                  <CardContent className="pt-6">
                    <div className="w-12 h-12 bg-gradient-to-br from-success-100 to-success-200 rounded-xl flex items-center justify-center mx-auto mb-4">
                      <svg className="w-6 h-6 text-success-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                    <div className="text-3xl font-bold text-neutral-900 mb-1">
                      {departments.reduce((sum, dept) => sum + dept.active_employees, 0)}
                    </div>
                    <div className="text-sm text-neutral-600 font-medium">Active Employees</div>
                    <Badge variant="success" size="sm" className="mt-2">
                      Online Now
                    </Badge>
                  </CardContent>
                </Card>

                <Card className="text-center">
                  <CardContent className="pt-6">
                    <div className="w-12 h-12 bg-gradient-to-br from-primary-100 to-primary-200 rounded-xl flex items-center justify-center mx-auto mb-4">
                      <svg className="w-6 h-6 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                      </svg>
                    </div>
                    <div className="text-3xl font-bold text-neutral-900 mb-1">
                      {departments.reduce((sum, dept) => sum + dept.total_employees, 0)}
                    </div>
                    <div className="text-sm text-neutral-600 font-medium">Total Employees</div>
                    <Badge variant="primary" size="sm" className="mt-2">
                      All Staff
                    </Badge>
                  </CardContent>
                </Card>

                <Card className="text-center">
                  <CardContent className="pt-6">
                    <div className="w-12 h-12 bg-gradient-to-br from-emerald-100 to-emerald-200 rounded-xl flex items-center justify-center mx-auto mb-4">
                      <svg className="w-6 h-6 text-emerald-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                      </svg>
                    </div>
                    <div className="text-3xl font-bold text-neutral-900 mb-1">{departments.length}</div>
                    <div className="text-sm text-neutral-600 font-medium">Departments</div>
                    <Badge variant="secondary" size="sm" className="mt-2">
                      Active Teams
                    </Badge>
                  </CardContent>
                </Card>

                <Card className="text-center">
                  <CardContent className="pt-6">
                    <div className="w-12 h-12 bg-gradient-to-br from-accent-100 to-accent-200 rounded-xl flex items-center justify-center mx-auto mb-4">
                      <svg className="w-6 h-6 text-accent-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                      </svg>
                    </div>
                    <div className="text-3xl font-bold text-neutral-900 mb-1">
                      {departments.length > 0 ? Math.round(
                        (departments.reduce((sum, dept) => sum + dept.active_employees, 0) /
                          departments.reduce((sum, dept) => sum + dept.total_employees, 0)) * 100
                      ) : 0}%
                    </div>
                    <div className="text-sm text-neutral-600 font-medium">Activity Rate</div>
                    <Badge variant="warning" size="sm" className="mt-2">
                      Average
                    </Badge>
                  </CardContent>
                </Card>
              </div>
            </div>
          )}
        </div>
      </main>
    </div>
  );
};

export default Dashboard;
