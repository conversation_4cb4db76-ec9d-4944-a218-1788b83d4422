import React, { useState } from 'react';
import { AuthProvider, useAuth } from './contexts/AuthContext';
import AuthForm from './components/AuthForm';
import Dashboard from './components/Dashboard';
import DepartmentView from './components/DepartmentView';
import StatusUpdateForm, { EnhancedTaskData } from './components/StatusUpdateForm';
import { ErrorBoundary, FloatingHeader } from './components/ui';
import { API_BASE_URL } from './config/api';

type AppView = 'dashboard' | 'department' | 'statusUpdate';

interface AppState {
  view: AppView;
  selectedDepartmentId?: string;
  selectedEmployeeId?: string;
  previousView?: AppView;
  previousDepartmentId?: string;
}

const AppContent: React.FC = () => {
  const { user } = useAuth();
  const [appState, setAppState] = useState<AppState>({
    view: 'dashboard'
  });

  const handleDepartmentSelect = (departmentId: string) => {
    setAppState({
      view: 'department',
      selectedDepartmentId: departmentId
    });
  };

  const handleBackToDashboard = () => {
    setAppState({
      view: 'dashboard'
    });
  };

  const handleUpdateStatus = () => {
    // Only allow users to update their own status
    if (!user) return;

    setAppState({
      ...appState,
      view: 'statusUpdate',
      selectedEmployeeId: user.id,
      previousView: appState.view,
      previousDepartmentId: appState.selectedDepartmentId
    });
  };

  const handleStatusSubmit = async (taskData: EnhancedTaskData) => {
    try {
      const token = localStorage.getItem('token');
      if (!token) {
        console.error('No authentication token found');
        return;
      }

      // Transform the frontend data to match the backend API format
      const apiPayload: any = {
        task_description: taskData.task,
        status: taskData.status,
        priority: taskData.priority,
        category: taskData.taskCategory,
        estimated_duration_minutes: taskData.taskDurationMinutes,
        progress_percentage: taskData.progressPercentage,
        tags: taskData.tags.map(tag => tag.label)
      };

      // Only include optional fields if they have valid values
      if (taskData.relatedProject && taskData.relatedProject.trim()) {
        // relatedProject now contains the project UUID from the dropdown
        apiPayload.project_id = taskData.relatedProject;
      }

      if (taskData.blockingIssues && taskData.blockingIssues.trim()) {
        apiPayload.blocking_issues = taskData.blockingIssues;
      }

      if (taskData.expectedFinishDateTime) {
        // Send as expected_finish_datetime to match backend validation
        apiPayload.expected_finish_datetime = taskData.expectedFinishDateTime;
      }

      if (taskData.numberOfQuestions) {
        apiPayload.number_of_questions = taskData.numberOfQuestions;
      }

      console.log('Submitting task update:', apiPayload);

      const response = await fetch(`${API_BASE_URL}/api/tasks/update`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(apiPayload),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        console.error('Task update failed:', errorData);

        // Show detailed error information
        if (errorData.errors && Array.isArray(errorData.errors)) {
          console.error('Validation errors:', errorData.errors);
          errorData.errors.forEach((error: any) => {
            console.error(`Field: ${error.field}, Message: ${error.message}`);
          });
        }

        // You might want to show an error message to the user here
        alert(`Task update failed: ${errorData.message || 'Unknown error'}`);
        return;
      }

      const result = await response.json();
      console.log('Task updated successfully:', result);

      // Restore the previous view state
      const previousView = appState.previousView || 'dashboard';
      const previousDepartmentId = appState.previousDepartmentId;

      setAppState({
        view: previousView,
        selectedDepartmentId: previousDepartmentId,
        selectedEmployeeId: undefined,
        previousView: undefined,
        previousDepartmentId: undefined
      });

    } catch (error) {
      console.error('Error updating task:', error);
      // You might want to show an error message to the user here
    }
  };

  const handleStatusCancel = () => {
    // Restore the previous view state
    const previousView = appState.previousView || 'dashboard';
    const previousDepartmentId = appState.previousDepartmentId;

    setAppState({
      view: previousView,
      selectedDepartmentId: previousDepartmentId,
      selectedEmployeeId: undefined,
      previousView: undefined,
      previousDepartmentId: undefined
    });
  };

  const handleFloatingHeaderStatusUpdate = () => {
    // Open the status update form for the current user
    if (!user) return;

    setAppState({
      ...appState,
      view: 'statusUpdate',
      selectedEmployeeId: user.id,
      previousView: appState.view,
      previousDepartmentId: appState.selectedDepartmentId
    });
  };

  if (!user) {
    return <AuthForm />;
  }

  return (
    <>
      {appState.view === 'dashboard' && (
        <Dashboard onDepartmentSelect={handleDepartmentSelect} />
      )}

      {appState.view === 'department' && appState.selectedDepartmentId && (
        <DepartmentView
          departmentId={appState.selectedDepartmentId}
          onBack={handleBackToDashboard}
          onUpdateStatus={handleUpdateStatus}
        />
      )}

      {appState.view === 'statusUpdate' && appState.selectedEmployeeId && user?.id === appState.selectedEmployeeId && (
        <StatusUpdateForm
          employeeId={appState.selectedEmployeeId}
          currentTask="" // TODO: Fetch current task from API based on employeeId
          onSubmit={handleStatusSubmit}
          onCancel={handleStatusCancel}
        />
      )}

      {/* Floating Header - appears on all authenticated views */}
      <FloatingHeader onStatusUpdate={handleFloatingHeaderStatusUpdate} />
    </>
  );
};

function App() {
  return (
    <ErrorBoundary>
      <AuthProvider>
        <AppContent />
      </AuthProvider>
    </ErrorBoundary>
  );
}

export default App;
