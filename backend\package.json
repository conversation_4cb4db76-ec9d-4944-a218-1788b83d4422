{"name": "employee-task-dashboard-backend", "version": "1.0.0", "description": "Backend API for Employee Task Dashboard with real-time features and analytics", "main": "dist/server.js", "scripts": {"dev": "nodemon src/server.ts", "build": "tsc -p tsconfig.build.json", "build:alias": "tsc -p tsconfig.build.json && tsc-alias -p tsconfig.build.json", "start": "node start.js", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "db:init": "npm run build && node dist/scripts/initDb.js", "db:migrate": "node dist/scripts/migrate.js", "db:seed": "node dist/scripts/seed.js", "db:reset": "npm run db:migrate && npm run db:seed"}, "keywords": ["employee", "task", "dashboard", "analytics", "real-time", "typescript", "express", "postgresql"], "author": "Your Company", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "compression": "^1.7.4", "morgan": "^1.10.0", "dotenv": "^16.3.1", "pg": "^8.11.3", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "joi": "^17.11.0", "socket.io": "^4.7.4", "redis": "^4.6.10", "winston": "^3.11.0", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "multer": "^1.4.5-lts.1", "uuid": "^9.0.1", "date-fns": "^2.30.0"}, "devDependencies": {"@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/compression": "^1.7.5", "@types/morgan": "^1.9.9", "@types/pg": "^8.10.9", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.5", "@types/multer": "^1.4.11", "@types/uuid": "^9.0.7", "@types/node": "^20.10.4", "@types/jest": "^29.5.8", "@types/supertest": "^2.0.16", "typescript": "^5.3.3", "nodemon": "^3.0.2", "ts-node": "^10.9.1", "eslint": "^8.55.0", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "jest": "^29.7.0", "ts-jest": "^29.1.1", "supertest": "^6.3.3", "tsconfig-paths": "^4.2.0", "tsc-alias": "^1.8.8"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}}