/**
 * Time utility functions for consistent 12-hour format handling
 */

/**
 * Format a date to 12-hour time format (e.g., "2:30 PM")
 * @param date - Date object or date string
 * @returns Formatted time string in 12-hour format
 */
export function formatTime12Hour(date: Date | string): string {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  
  if (isNaN(dateObj.getTime())) {
    return 'Invalid Time';
  }

  return dateObj.toLocaleTimeString('en-US', {
    hour: 'numeric',
    minute: '2-digit',
    hour12: true
  });
}

/**
 * Format a date to 12-hour time format with seconds (e.g., "2:30:45 PM")
 * @param date - Date object or date string
 * @returns Formatted time string in 12-hour format with seconds
 */
export function formatTime12HourWithSeconds(date: Date | string): string {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  
  if (isNaN(dateObj.getTime())) {
    return 'Invalid Time';
  }

  return dateObj.toLocaleTimeString('en-US', {
    hour: 'numeric',
    minute: '2-digit',
    second: '2-digit',
    hour12: true
  });
}

/**
 * Format a date to include both date and 12-hour time (e.g., "Jan 15, 2024 at 2:30 PM")
 * @param date - Date object or date string
 * @returns Formatted date and time string
 */
export function formatDateTime12Hour(date: Date | string): string {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  
  if (isNaN(dateObj.getTime())) {
    return 'Invalid Date';
  }

  const dateStr = dateObj.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });

  const timeStr = formatTime12Hour(dateObj);

  return `${dateStr} at ${timeStr}`;
}

/**
 * Format a date to short date format (e.g., "Jan 15, 2024")
 * @param date - Date object or date string
 * @returns Formatted date string
 */
export function formatDate(date: Date | string): string {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  
  if (isNaN(dateObj.getTime())) {
    return 'Invalid Date';
  }

  return dateObj.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });
}

/**
 * Format a date to relative time (e.g., "2 hours ago", "in 3 days")
 * @param date - Date object or date string
 * @returns Relative time string
 */
export function formatRelativeTime(date: Date | string): string {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  
  if (isNaN(dateObj.getTime())) {
    return 'Invalid Date';
  }

  const now = new Date();
  const diffMs = dateObj.getTime() - now.getTime();
  const diffMinutes = Math.floor(diffMs / (1000 * 60));
  const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

  if (Math.abs(diffMinutes) < 1) {
    return 'Just now';
  } else if (Math.abs(diffMinutes) < 60) {
    return diffMinutes > 0 ? `in ${diffMinutes} minutes` : `${Math.abs(diffMinutes)} minutes ago`;
  } else if (Math.abs(diffHours) < 24) {
    return diffHours > 0 ? `in ${diffHours} hours` : `${Math.abs(diffHours)} hours ago`;
  } else if (Math.abs(diffDays) < 7) {
    return diffDays > 0 ? `in ${diffDays} days` : `${Math.abs(diffDays)} days ago`;
  } else {
    return formatDate(dateObj);
  }
}

/**
 * Get current time in 12-hour format
 * @returns Current time string in 12-hour format
 */
export function getCurrentTime12Hour(): string {
  return formatTime12Hour(new Date());
}

/**
 * Get current time in 12-hour format with seconds
 * @returns Current time string in 12-hour format with seconds
 */
export function getCurrentTime12HourWithSeconds(): string {
  return formatTime12HourWithSeconds(new Date());
}

/**
 * Convert datetime-local input value to 12-hour format display
 * @param datetimeLocal - datetime-local input value (YYYY-MM-DDTHH:mm)
 * @returns Formatted date and time in 12-hour format
 */
export function formatDateTimeLocal12Hour(datetimeLocal: string): string {
  if (!datetimeLocal) return '';
  
  const date = new Date(datetimeLocal);
  return formatDateTime12Hour(date);
}

/**
 * Check if a time is in AM or PM
 * @param date - Date object or date string
 * @returns 'AM' or 'PM'
 */
export function getAmPm(date: Date | string): 'AM' | 'PM' {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  
  if (isNaN(dateObj.getTime())) {
    return 'AM';
  }

  return dateObj.getHours() >= 12 ? 'PM' : 'AM';
}

/**
 * Format duration in minutes to human readable format
 * @param minutes - Duration in minutes
 * @returns Human readable duration string
 */
export function formatDuration(minutes: number): string {
  if (minutes < 60) {
    return `${minutes} minute${minutes !== 1 ? 's' : ''}`;
  }
  
  const hours = Math.floor(minutes / 60);
  const remainingMinutes = minutes % 60;
  
  if (remainingMinutes === 0) {
    return `${hours} hour${hours !== 1 ? 's' : ''}`;
  }
  
  return `${hours} hour${hours !== 1 ? 's' : ''} ${remainingMinutes} minute${remainingMinutes !== 1 ? 's' : ''}`;
}
