import { Router } from 'express';
import { query, validationResult } from 'express-validator';
import { 
  getProductivityAnalytics, 
  getTaskCategoryAnalytics, 
  getDepartmentComparison,
  getRealtimeAnalytics 
} from '@/controllers/analyticsController';
import { authenticateToken, requireManager } from '@/middleware/auth';

const router = Router();

// =====================================================
// VALIDATION RULES
// =====================================================

const dateRangeValidation = [
  query('start_date')
    .isISO8601()
    .withMessage('Start date must be a valid ISO 8601 date'),
  query('end_date')
    .isISO8601()
    .withMessage('End date must be a valid ISO 8601 date'),
  query('period')
    .optional()
    .isIn(['daily', 'weekly', 'monthly'])
    .withMessage('Period must be daily, weekly, or monthly')
];

const productivityAnalyticsValidation = [
  ...dateRangeValidation,
  query('user_id')
    .optional()
    .isUUID()
    .withMessage('User ID must be a valid UUID'),
  query('department_id')
    .optional()
    .isUUID()
    .withMessage('Department ID must be a valid UUID')
];

const categoryAnalyticsValidation = [
  ...dateRangeValidation,
  query('department_id')
    .optional()
    .isUUID()
    .withMessage('Department ID must be a valid UUID')
];

// =====================================================
// VALIDATION MIDDLEWARE
// =====================================================

const handleValidationErrors = (req: any, res: any, next: any) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      message: 'Validation failed',
      errors: errors.array().map(error => ({
        field: (error as any).path || (error as any).param || 'unknown',
        message: error.msg,
        code: 'VALIDATION_ERROR'
      }))
    });
  }
  next();
};

// =====================================================
// ROUTES
// =====================================================

/**
 * @route   GET /api/analytics/productivity
 * @desc    Get productivity analytics for user or department
 * @access  Private (Manager+ for department data, users can see own data)
 * @query   start_date - Start date (ISO 8601)
 * @query   end_date - End date (ISO 8601)
 * @query   period - Grouping period (daily, weekly, monthly)
 * @query   user_id - Specific user ID (optional)
 * @query   department_id - Specific department ID (optional)
 * @returns Productivity analytics with trends and summary
 */
router.get('/productivity',
  authenticateToken,
  productivityAnalyticsValidation,
  handleValidationErrors,
  getProductivityAnalytics
);

/**
 * @route   GET /api/analytics/task-categories
 * @desc    Get task category performance analytics
 * @access  Private (Manager+ for department data)
 * @query   start_date - Start date (ISO 8601)
 * @query   end_date - End date (ISO 8601)
 * @query   department_id - Specific department ID (optional)
 * @returns Task category analytics with completion rates and trends
 */
router.get('/task-categories',
  authenticateToken,
  requireManager,
  categoryAnalyticsValidation,
  handleValidationErrors,
  getTaskCategoryAnalytics
);

/**
 * @route   GET /api/analytics/departments
 * @desc    Get department comparison analytics
 * @access  Private (Admin only)
 * @query   start_date - Start date (ISO 8601)
 * @query   end_date - End date (ISO 8601)
 * @returns Department comparison metrics
 */
router.get('/departments',
  authenticateToken,
  requireManager, // Will be further restricted to admin in controller
  dateRangeValidation,
  handleValidationErrors,
  getDepartmentComparison
);

/**
 * @route   GET /api/analytics/realtime
 * @desc    Get real-time analytics summary
 * @access  Private (Manager+)
 * @returns Current day statistics and real-time metrics
 */
router.get('/realtime',
  authenticateToken,
  requireManager,
  getRealtimeAnalytics
);

export default router;
