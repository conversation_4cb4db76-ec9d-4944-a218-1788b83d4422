import React, { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { <PERSON>ton, Card, CardContent, Input, StatusIndicator, Badge, EmptyState } from './ui';
import { API_BASE_URL } from '../config/api';
import { formatDateTime12Hour } from '../utils/time';
import { getTaskStatusText, isTaskExpired, getTimeRemaining, fetchCurrentTask } from '../utils/taskUtils';
import EmployeeDetailsModal from './EmployeeDetailsModal';

interface Employee {
  id: string;
  name: string;
  email: string;
  role: string;
  task_description: string;
  status: 'active' | 'idle' | 'offline';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  category: string;
  progress_percentage: number;
  project_name: string;
  expected_completion_date: string;
  expected_finish_datetime?: string;
  blocking_issues: string;
  last_updated: string;
  minutes_since_update: number;
}

interface DepartmentViewProps {
  departmentId: string;
  onBack: () => void;
  onUpdateStatus: () => void;
}

const DepartmentView: React.FC<DepartmentViewProps> = ({ departmentId, onBack, onUpdateStatus }) => {
  const { user } = useAuth();
  const [searchTerm, setSearchTerm] = useState('');

  // Mock department data
  const departmentNames: { [key: string]: string } = {
    'dept-1': 'Engineering',
    'dept-2': 'Marketing',
    'dept-3': 'Sales',
    'dept-4': 'HR',
    'dept-5': 'Finance'
  };

  // TODO: Replace with real API call to fetch employees
  const [employees, setEmployees] = useState<Employee[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Employee details modal state
  const [selectedEmployee, setSelectedEmployee] = useState<Employee | null>(null);
  const [employeeTaskDetails, setEmployeeTaskDetails] = useState<any>(null);
  const [isDetailsModalOpen, setIsDetailsModalOpen] = useState(false);
  const [isLoadingTaskDetails, setIsLoadingTaskDetails] = useState(false);

  useEffect(() => {
    const fetchEmployees = async () => {
      try {
        setIsLoading(true);
        setError(null);

        console.log('Fetching employees for department ID:', departmentId);

        const token = localStorage.getItem('token');
        if (!token) {
          setError('Authentication required');
          return;
        }

        // Check if departmentId is a UUID (real ID) or mock ID
        const isUUID = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(departmentId);

        if (!isUUID) {
          console.warn('Department ID is not a UUID, this suggests mock data is being used:', departmentId);
          setError(`Invalid department ID format. Expected UUID but got: ${departmentId}`);
          return;
        }

        const url = `${API_BASE_URL}/api/departments/${departmentId}/employees`;
        console.log('Making request to:', url);

        const response = await fetch(url, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        });

        console.log('Response status:', response.status);

        if (!response.ok) {
          const errorText = await response.text();
          console.error('Error response:', errorText);
          throw new Error(`HTTP error! status: ${response.status} - ${errorText}`);
        }

        const data = await response.json();
        console.log('Response data:', data);

        if (data.success && Array.isArray(data.data)) {
          console.log('Employee data received:', data.data);
          // Log status values to debug the StatusIndicator issue
          data.data.forEach((emp: any, index: number) => {
            console.log(`Employee ${index + 1} status:`, emp.status, typeof emp.status);
          });
          setEmployees(data.data);
        } else {
          console.error('Invalid response format:', data);
          setError('Invalid response from server');
        }
      } catch (err) {
        setError('Failed to load employees');
        console.error('Error fetching employees:', err);
      } finally {
        setIsLoading(false);
      }
    };

    fetchEmployees();
  }, [departmentId]);

  const filteredEmployees = employees.filter(employee =>
    employee.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    employee.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (employee.task_description && employee.task_description.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  // Handle viewing employee details
  const handleViewDetails = async (employee: Employee) => {
    setSelectedEmployee(employee);
    setIsDetailsModalOpen(true);
    setIsLoadingTaskDetails(true);
    setEmployeeTaskDetails(null);

    // Debug logging
    console.log('Attempting to fetch task details for employee:', {
      employeeId: employee.id,
      employeeName: employee.name,
      currentUser: localStorage.getItem('user'),
      token: localStorage.getItem('token') ? 'Present' : 'Missing'
    });

    try {
      const taskResponse = await fetchCurrentTask(employee.id);
      console.log('Task response:', taskResponse);
      if (taskResponse && taskResponse.success && taskResponse.data) {
        setEmployeeTaskDetails(taskResponse.data);
      }
    } catch (error) {
      console.error('Error fetching task details:', error);
    } finally {
      setIsLoadingTaskDetails(false);
    }
  };

  const handleCloseDetailsModal = () => {
    setIsDetailsModalOpen(false);
    setSelectedEmployee(null);
    setEmployeeTaskDetails(null);
    setIsLoadingTaskDetails(false);
  };

  return (
    <div className="min-h-screen bg-neutral-50 pb-32">
      {/* Modern Header */}
      <header className="bg-white border-b border-neutral-200 sticky top-0 z-30 backdrop-blur-md bg-white/95">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center space-x-4">
              <Button
                variant="secondary"
                size="sm"
                onClick={onBack}
                icon={
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                  </svg>
                }
                iconPosition="left"
              >
                Back to Dashboard
              </Button>
              <div className="h-6 w-px bg-neutral-300" />
              <div>
                <h1 className="text-2xl font-bold text-neutral-900">{departmentNames[departmentId]}</h1>
                <p className="text-sm text-neutral-600">Department Overview & Employee Status</p>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <Badge variant="primary" size="sm">
                {filteredEmployees.length} Employees
              </Badge>
              <Badge variant="success" size="sm">
                {filteredEmployees.filter(emp => emp.status === 'active').length} Active
              </Badge>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
        <div className="space-y-8">
          {/* Search and Filters */}
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div className="flex-1 max-w-lg">
              <Input
                id="search"
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                placeholder="Search by name, email, or task..."
                icon={
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                }
              />
            </div>
            <div className="flex items-center space-x-3">
              <div className="text-sm text-neutral-600">
                Showing {filteredEmployees.length} of {employees.length} employees
              </div>
            </div>
          </div>

          {/* Employee List */}
          <div className="grid gap-6 lg:grid-cols-1">
            {isLoading ? (
              <div className="space-y-4">
                {[...Array(3)].map((_, i) => (
                  <Card key={i} className="animate-pulse">
                    <CardContent className="p-6">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-4">
                          <div className="w-12 h-12 bg-neutral-200 rounded-xl"></div>
                          <div className="flex-1 space-y-2">
                            <div className="h-4 bg-neutral-200 rounded w-1/3"></div>
                            <div className="h-3 bg-neutral-200 rounded w-1/2"></div>
                            <div className="h-3 bg-neutral-200 rounded w-3/4"></div>
                          </div>
                        </div>
                        <div className="space-y-2">
                          <div className="h-3 bg-neutral-200 rounded w-20"></div>
                          <div className="h-3 bg-neutral-200 rounded w-16"></div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            ) : error ? (
              <Card className="text-center py-12">
                <CardContent>
                  <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg className="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                  <h3 className="text-lg font-semibold text-neutral-900 mb-2">Failed to Load Employees</h3>
                  <p className="text-neutral-600 mb-4">{error}</p>
                  <Button onClick={() => window.location.reload()}>Try Again</Button>
                </CardContent>
              </Card>
            ) : employees.length === 0 ? (
              <EmptyState
                icon={
                  <svg className="w-12 h-12 text-neutral-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                  </svg>
                }
                title="No Employees Found"
                description="This department doesn't have any employees yet. Contact your administrator to add employees to this department."
                action={{
                  label: "Refresh",
                  onClick: () => window.location.reload(),
                  variant: "secondary"
                }}
              />
            ) : filteredEmployees.length === 0 ? (
              <Card className="text-center py-12">
                <CardContent>
                  <svg className="w-12 h-12 text-neutral-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                  <h3 className="text-lg font-medium text-neutral-900 mb-2">No employees found</h3>
                  <p className="text-neutral-600">Try adjusting your search criteria.</p>
                </CardContent>
              </Card>
            ) : (
              <div className="space-y-4">
                {filteredEmployees.map((employee) => (
                  <Card key={employee.id} className="hover-lift">
                    <CardContent className="p-6">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-4">
                          <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-primary-100 to-primary-200 flex items-center justify-center">
                            <span className="text-lg font-semibold text-primary-600">
                              {employee.name.split(' ').map(n => n[0]).join('')}
                            </span>
                          </div>
                          <div className="flex-1">
                            <div className="flex items-center space-x-3 mb-1">
                              <h3 className="text-lg font-semibold text-neutral-900">{employee.name}</h3>
                              <StatusIndicator
                                status={employee.status || 'offline'}
                                size="sm"
                              />
                            </div>
                            <p className="text-sm text-neutral-600 mb-2">{employee.email}</p>
                            <div className="flex items-center text-sm text-neutral-500">
                              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                              </svg>
                              <span className="font-medium">Current Task:</span>
                              <span className={`ml-1 ${isTaskExpired(employee.expected_finish_datetime) ? 'text-orange-600 font-medium' : ''}`}>
                                {getTaskStatusText(!!employee.task_description, employee.task_description, employee.expected_finish_datetime)}
                              </span>
                              {employee.expected_finish_datetime && (
                                <span className={`ml-2 text-xs px-2 py-1 rounded-full ${
                                  isTaskExpired(employee.expected_finish_datetime)
                                    ? 'bg-orange-100 text-orange-700'
                                    : 'bg-blue-100 text-blue-700'
                                }`}>
                                  {getTimeRemaining(employee.expected_finish_datetime)}
                                </span>
                              )}
                            </div>
                          </div>
                        </div>
                        <div className="flex items-center space-x-3">
                          <div className="text-right">
                            <div className="text-xs text-neutral-500 mb-1">Last Updated</div>
                            <div className="text-sm font-medium text-neutral-700">
                              {employee.last_updated
                                ? formatDateTime12Hour(employee.last_updated)
                                : 'Never'}
                            </div>
                          </div>
                          <Button
                            variant="secondary"
                            size="sm"
                            onClick={() => handleViewDetails(employee)}
                            icon={
                              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                              </svg>
                            }
                          >
                            View Details
                          </Button>
                          {user?.id === employee.id && (
                            <Button
                              variant="primary"
                              size="sm"
                              onClick={() => onUpdateStatus()}
                              icon={
                                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                                </svg>
                              }
                            >
                              Update My Status
                            </Button>
                          )}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </div>
        </div>
      </main>

      {/* Employee Details Modal */}
      <EmployeeDetailsModal
        isOpen={isDetailsModalOpen}
        onClose={handleCloseDetailsModal}
        employee={selectedEmployee}
        taskDetails={employeeTaskDetails}
        isLoading={isLoadingTaskDetails}
      />
    </div>
  );
};

export default DepartmentView;
