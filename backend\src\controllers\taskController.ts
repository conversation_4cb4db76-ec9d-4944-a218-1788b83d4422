import { Request, Response } from 'express';
import { query, transaction } from '@/config/database';
import { logger } from '@/utils/logger';
import { TaskUpdateRequest, TaskUpdateResponse } from '@/types/api';
import { CreateTaskUpdateData, UpdateTaskUpdateData } from '@/types/database';
import { taskScheduler } from '../services/taskSchedulerService';

// =====================================================
// TASK CONTROLLERS
// =====================================================

// Get current task for user
export const getCurrentTask = async (req: Request, res: Response): Promise<void> => {
  try {
    const { userId } = req.params;

    // Enhanced logging for debugging access issues
    logger.info('getCurrentTask access check', {
      requestingUserId: req.user!.id,
      requestingUserRole: req.user!.role,
      requestingUserDept: req.user!.department_id,
      targetUserId: userId
    });

    // Check if user can access this task
    // Note: The requireUserAccess middleware already handles basic access control
    // This controller adds additional department-level restrictions
    if (req.user!.id !== userId && req.user!.role !== 'admin') {
      // For managers and employees, check if they're in the same department
      if (req.user!.role === 'manager' || req.user!.role === 'employee') {
        try {
          const sameDepart = await isUserInSameDepartment(req.user!.id, userId);
          logger.info('Department access check', {
            requestingUserId: req.user!.id,
            requestingUserRole: req.user!.role,
            requestingUserDept: req.user!.department_id,
            targetUserId: userId,
            sameDepart
          });

          if (!sameDepart) {
            logger.warn('User accessing employee from different department', {
              requestingUserId: req.user!.id,
              requestingUserRole: req.user!.role,
              requestingUserDept: req.user!.department_id,
              targetUserId: userId
            });
            res.status(403).json({
              success: false,
              message: 'Access denied - not in same department'
            });
            return;
          }
        } catch (error) {
          logger.error('Department check failed', {
            error: error instanceof Error ? error.message : 'Unknown error',
            requestingUserId: req.user!.id,
            targetUserId: userId
          });
          res.status(500).json({
            success: false,
            message: 'Error checking department access'
          });
          return;
        }
      } else {
        // Unknown role trying to access other user's data
        res.status(403).json({
          success: false,
          message: 'Access denied - insufficient permissions'
        });
        return;
      }
    }

    const result = await query(`
      SELECT
        tu.*,
        p.name as project_name,
        array_agg(
          CASE WHEN tt.id IS NOT NULL THEN
            json_build_object('id', tt.id, 'name', tt.name, 'color', tt.color)
          END
        ) FILTER (WHERE tt.id IS NOT NULL) as tags
      FROM task_updates tu
      LEFT JOIN projects p ON tu.project_id = p.id
      LEFT JOIN task_update_tags tut ON tu.id = tut.task_update_id
      LEFT JOIN task_tags tt ON tut.task_tag_id = tt.id
      WHERE tu.user_id = $1
      GROUP BY tu.id, p.name, tu.updated_at
      ORDER BY tu.updated_at DESC
      LIMIT 1
    `, [userId]);

    if (result.rows.length === 0) {
      res.json({
        success: true,
        data: null,
        message: 'No current task found'
      });
      return;
    }

    const task = result.rows[0];
    const response: TaskUpdateResponse = {
      id: task.id,
      task_description: task.task_description,
      status: task.status,
      priority: task.priority,
      category: task.category,
      estimated_duration_minutes: task.estimated_duration_minutes,
      progress_percentage: task.progress_percentage,
      project_id: task.project_id,
      project_name: task.project_name,
      expected_completion_date: task.expected_completion_date?.toISOString(),
      expected_finish_datetime: task.expected_finish_datetime?.toISOString(),
      blocking_issues: task.blocking_issues,
      number_of_questions: task.number_of_questions,
      tags: task.tags || [],
      created_at: task.created_at.toISOString(),
      updated_at: task.updated_at.toISOString()
    };

    res.json({
      success: true,
      data: response
    });
  } catch (error) {
    logger.error('Get current task error', {
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
      userId: req.params.userId,
      requestUser: req.user?.id
    });
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

// Update or create task
export const updateTask = async (req: Request, res: Response): Promise<void> => {
  try {
    const userId = req.user!.id;
    const taskData: TaskUpdateRequest = req.body;

    // Validate estimated duration if provided
    if (taskData.estimated_duration_minutes && taskData.estimated_duration_minutes < 0) {
      res.status(400).json({
        success: false,
        message: 'Estimated duration must be positive'
      });
      return;
    }

    // Validate progress percentage
    if (taskData.progress_percentage < 0 || taskData.progress_percentage > 100) {
      res.status(400).json({
        success: false,
        message: 'Progress percentage must be between 0 and 100'
      });
      return;
    }

    // Validate project exists if provided
    if (taskData.project_id) {
      const projectResult = await query(
        'SELECT id FROM projects WHERE id = $1 AND is_active = true',
        [taskData.project_id]
      );

      if (projectResult.rows.length === 0) {
        res.status(400).json({
          success: false,
          message: 'Invalid project ID'
        });
        return;
      }
    }

    const taskId = await transaction(async (client) => {
      // Check if user already has a task
      const existingTask = await client.query(
        'SELECT id FROM task_updates WHERE user_id = $1',
        [userId]
      );

      let taskId: string;

      if (existingTask.rows.length > 0) {
        // Update existing task
        taskId = existingTask.rows[0].id;
        
        const updateData: UpdateTaskUpdateData = {
          task_description: taskData.task_description,
          status: taskData.status,
          priority: taskData.priority,
          category: taskData.category,
          estimated_duration_minutes: taskData.estimated_duration_minutes,
          progress_percentage: taskData.progress_percentage,
          project_id: taskData.project_id || null,
          expected_completion_date: taskData.expected_completion_date ? new Date(taskData.expected_completion_date) : null,
          blocking_issues: taskData.blocking_issues || null,
          number_of_questions: taskData.number_of_questions || null,
          expected_finish_datetime: taskData.expected_finish_datetime ? new Date(taskData.expected_finish_datetime) : null
        };

        await client.query(`
          UPDATE task_updates SET
            task_description = $2,
            status = $3,
            priority = $4,
            category = $5,
            estimated_duration_minutes = $6,
            progress_percentage = $7,
            project_id = $8,
            expected_completion_date = $9,
            blocking_issues = $10,
            number_of_questions = $11,
            expected_finish_datetime = $12,
            updated_at = CURRENT_TIMESTAMP
          WHERE id = $1
        `, [
          taskId,
          updateData.task_description,
          updateData.status,
          updateData.priority,
          updateData.category,
          updateData.estimated_duration_minutes,
          updateData.progress_percentage,
          updateData.project_id,
          updateData.expected_completion_date,
          updateData.blocking_issues,
          updateData.number_of_questions,
          updateData.expected_finish_datetime
        ]);
      } else {
        // Create new task
        const createData: CreateTaskUpdateData = {
          user_id: userId,
          task_description: taskData.task_description,
          status: taskData.status,
          priority: taskData.priority,
          category: taskData.category,
          estimated_duration_minutes: taskData.estimated_duration_minutes,
          progress_percentage: taskData.progress_percentage,
          project_id: taskData.project_id || undefined,
          expected_completion_date: taskData.expected_completion_date ? new Date(taskData.expected_completion_date) : undefined,
          blocking_issues: taskData.blocking_issues || undefined,
          number_of_questions: taskData.number_of_questions || undefined,
          expected_finish_datetime: taskData.expected_finish_datetime ? new Date(taskData.expected_finish_datetime) : undefined
        };

        const insertResult = await client.query(`
          INSERT INTO task_updates (
            user_id, task_description, status, priority, category,
            estimated_duration_minutes, progress_percentage, project_id,
            expected_completion_date, blocking_issues, number_of_questions,
            expected_finish_datetime, created_at, updated_at
          ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
          RETURNING id
        `, [
          createData.user_id,
          createData.task_description,
          createData.status,
          createData.priority,
          createData.category,
          createData.estimated_duration_minutes,
          createData.progress_percentage,
          createData.project_id,
          createData.expected_completion_date,
          createData.blocking_issues,
          createData.number_of_questions,
          createData.expected_finish_datetime
        ]);

        taskId = insertResult.rows[0].id;
      }

      // Handle tags
      if (taskData.tags && taskData.tags.length > 0) {
        // Remove existing tags
        await client.query(
          'DELETE FROM task_update_tags WHERE task_update_id = $1',
          [taskId]
        );

        // Add new tags
        for (const tagName of taskData.tags) {
          // Get or create tag
          let tagResult = await client.query(
            'SELECT id FROM task_tags WHERE name = $1',
            [tagName.toLowerCase().trim()]
          );

          let tagId: string;
          if (tagResult.rows.length === 0) {
            // Create new tag
            const newTagResult = await client.query(`
              INSERT INTO task_tags (name, created_at)
              VALUES ($1, CURRENT_TIMESTAMP)
              RETURNING id
            `, [tagName.toLowerCase().trim()]);
            tagId = newTagResult.rows[0].id;
          } else {
            tagId = tagResult.rows[0].id;
          }

          // Link tag to task
          await client.query(`
            INSERT INTO task_update_tags (task_update_id, task_tag_id, created_at)
            VALUES ($1, $2, CURRENT_TIMESTAMP)
            ON CONFLICT (task_update_id, task_tag_id) DO NOTHING
          `, [taskId, tagId]);
        }
      }

      return taskId;
    });

    // Get updated task with all details
    const updatedTaskResult = await query(`
      SELECT
        tu.*,
        p.name as project_name,
        array_agg(
          CASE WHEN tt.id IS NOT NULL THEN
            json_build_object('id', tt.id, 'name', tt.name, 'color', tt.color)
          END
        ) FILTER (WHERE tt.id IS NOT NULL) as tags
      FROM task_updates tu
      LEFT JOIN projects p ON tu.project_id = p.id
      LEFT JOIN task_update_tags tut ON tu.id = tut.task_update_id
      LEFT JOIN task_tags tt ON tut.task_tag_id = tt.id
      WHERE tu.id = $1
      GROUP BY tu.id, p.name
    `, [taskId]);

    const task = updatedTaskResult.rows[0];
    const response: TaskUpdateResponse = {
      id: task.id,
      task_description: task.task_description,
      status: task.status,
      priority: task.priority,
      category: task.category,
      estimated_duration_minutes: task.estimated_duration_minutes,
      progress_percentage: task.progress_percentage,
      project_id: task.project_id,
      project_name: task.project_name,
      expected_completion_date: task.expected_completion_date?.toISOString(),
      expected_finish_datetime: task.expected_finish_datetime?.toISOString(),
      blocking_issues: task.blocking_issues,
      tags: task.tags || [],
      created_at: task.created_at.toISOString(),
      updated_at: task.updated_at.toISOString()
    };

    res.json({
      success: true,
      data: response,
      message: 'Task updated successfully'
    });
  } catch (error) {
    logger.error('Update task error', {
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
      userId: req.user?.id,
      taskData: req.body
    });
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

// Get task history for user
export const getTaskHistory = async (req: Request, res: Response): Promise<void> => {
  try {
    const { userId } = req.params;
    const { page = 1, limit = 20 } = req.query;

    // Check access permissions
    if (req.user!.id !== userId && req.user!.role !== 'admin') {
      if (req.user!.role === 'manager' || req.user!.role === 'employee') {
        try {
          const sameDepart = await isUserInSameDepartment(req.user!.id, userId);
          logger.info('Task history department access check', {
            requestingUserId: req.user!.id,
            requestingUserRole: req.user!.role,
            targetUserId: userId,
            sameDepart
          });

          if (!sameDepart) {
            logger.warn('User accessing task history from different department', {
              requestingUserId: req.user!.id,
              requestingUserRole: req.user!.role,
              targetUserId: userId
            });
            res.status(403).json({
              success: false,
              message: 'Access denied - not in same department'
            });
            return;
          }
        } catch (error) {
          logger.error('Department check failed for task history', {
            error: error instanceof Error ? error.message : 'Unknown error',
            requestingUserId: req.user!.id,
            targetUserId: userId
          });
          res.status(500).json({
            success: false,
            message: 'Error checking department access'
          });
          return;
        }
      } else {
        res.status(403).json({
          success: false,
          message: 'Access denied - insufficient permissions'
        });
        return;
      }
    }

    const offset = (Number(page) - 1) * Number(limit);

    const result = await query(`
      SELECT 
        th.*,
        p.name as project_name
      FROM task_history th
      LEFT JOIN projects p ON th.project_id = p.id
      WHERE th.user_id = $1
      ORDER BY th.created_at DESC
      LIMIT $2 OFFSET $3
    `, [userId, limit, offset]);

    const countResult = await query(
      'SELECT COUNT(*) FROM task_history WHERE user_id = $1',
      [userId]
    );

    const total = parseInt(countResult.rows[0].count);
    const totalPages = Math.ceil(total / Number(limit));

    const history = result.rows.map(row => ({
      id: row.id,
      task_description: row.task_description,
      status: row.status,
      priority: row.priority,
      category: row.category,
      estimated_duration_minutes: row.estimated_duration_minutes,
      progress_percentage: row.progress_percentage,
      project_name: row.project_name,
      expected_completion_date: row.expected_completion_date?.toISOString(),
      blocking_issues: row.blocking_issues,
      number_of_questions: row.number_of_questions,
      expected_finish_datetime: row.expected_finish_datetime?.toISOString(),
      action_type: row.action_type,
      session_duration_minutes: row.session_duration_minutes,
      created_at: row.created_at.toISOString()
    }));

    res.json({
      success: true,
      data: {
        data: history,
        pagination: {
          page: Number(page),
          limit: Number(limit),
          total,
          totalPages,
          hasNext: Number(page) < totalPages,
          hasPrev: Number(page) > 1
        }
      }
    });
  } catch (error) {
    logger.error('Get task history error', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

// Get available tags
export const getTags = async (req: Request, res: Response): Promise<void> => {
  try {
    const { search } = req.query;
    
    let whereClause = '';
    const params: any[] = [];
    
    if (search) {
      whereClause = 'WHERE name ILIKE $1';
      params.push(`%${search}%`);
    }

    const result = await query(`
      SELECT id, name, color, usage_count
      FROM task_tags
      ${whereClause}
      ORDER BY usage_count DESC, name ASC
      LIMIT 50
    `, params);

    const tags = result.rows.map(row => ({
      id: row.id,
      name: row.name,
      color: row.color,
      usage_count: row.usage_count
    }));

    res.json({
      success: true,
      data: tags
    });
  } catch (error) {
    logger.error('Get tags error', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

// Complete current task
export const completeTask = async (req: Request, res: Response): Promise<void> => {
  try {
    const userId = req.user!.id;
    const { taskId } = req.params;

    const success = await taskScheduler.completeTask(userId, taskId);

    if (success) {
      res.json({
        success: true,
        message: 'Task completed successfully'
      });
    } else {
      res.status(400).json({
        success: false,
        message: 'Failed to complete task'
      });
    }
  } catch (error) {
    logger.error('Complete task error', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

// Helper function to check if users are in same department
const isUserInSameDepartment = async (managerId: string, userId: string): Promise<boolean> => {
  try {
    const result = await query(`
      SELECT
        u1.department_id as manager_dept,
        u2.department_id as user_dept,
        u1.name as manager_name,
        u2.name as user_name
      FROM users u1, users u2
      WHERE u1.id = $1 AND u2.id = $2
    `, [managerId, userId]);

    logger.info('Department check query result', {
      managerId,
      userId,
      resultCount: result.rows.length,
      result: result.rows[0] || null
    });

    if (result.rows.length === 0) {
      logger.warn('No users found for department check', { managerId, userId });
      return false;
    }

    const sameDepart = result.rows[0].manager_dept === result.rows[0].user_dept;
    logger.info('Department comparison', {
      managerDept: result.rows[0].manager_dept,
      userDept: result.rows[0].user_dept,
      sameDepart
    });

    return sameDepart;
  } catch (error) {
    logger.error('Check same department error', error);
    return false;
  }
};

// Delete/discard a task
export const deleteTask = async (req: Request, res: Response): Promise<void> => {
  try {
    const { taskId } = req.params;
    const userId = req.user!.id;

    // First, get the task to verify ownership and get details for history
    const taskResult = await query(
      'SELECT * FROM task_updates WHERE id = $1',
      [taskId]
    );

    if (taskResult.rows.length === 0) {
      res.status(404).json({
        success: false,
        message: 'Task not found'
      });
      return;
    }

    const task = taskResult.rows[0];

    // Check if user owns the task or is admin
    if (task.user_id !== userId && req.user!.role !== 'admin') {
      res.status(403).json({
        success: false,
        message: 'Access denied - you can only delete your own tasks'
      });
      return;
    }

    await transaction(async (client) => {
      // Add entry to task history before deletion
      await client.query(`
        INSERT INTO task_history (
          user_id, task_update_id, task_description, status, priority, category,
          estimated_duration_minutes, progress_percentage, project_id,
          expected_completion_date, blocking_issues, number_of_questions,
          expected_finish_datetime, action_type, created_at
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, CURRENT_TIMESTAMP)
      `, [
        task.user_id,
        task.id,
        task.task_description,
        task.status,
        task.priority,
        task.category,
        task.estimated_duration_minutes,
        task.progress_percentage,
        task.project_id,
        task.expected_completion_date,
        task.blocking_issues,
        task.number_of_questions,
        task.expected_finish_datetime,
        'cancelled'
      ]);

      // Delete task-tag relationships
      await client.query(
        'DELETE FROM task_update_tags WHERE task_update_id = $1',
        [taskId]
      );

      // Delete the task
      await client.query(
        'DELETE FROM task_updates WHERE id = $1',
        [taskId]
      );
    });

    logger.info('Task deleted successfully', {
      taskId,
      userId,
      taskDescription: task.task_description
    });

    res.json({
      success: true,
      message: 'Task deleted successfully'
    });
  } catch (error) {
    logger.error('Delete task error', {
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
      taskId: req.params.taskId,
      userId: req.user?.id
    });
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

// Get specific task by ID
export const getTaskById = async (req: Request, res: Response): Promise<void> => {
  try {
    const { taskId } = req.params;
    const userId = req.user!.id;

    const result = await query(`
      SELECT
        tu.*,
        p.name as project_name,
        array_agg(
          CASE WHEN tt.id IS NOT NULL THEN
            json_build_object('id', tt.id, 'name', tt.name, 'color', tt.color)
          END
        ) FILTER (WHERE tt.id IS NOT NULL) as tags
      FROM task_updates tu
      LEFT JOIN projects p ON tu.project_id = p.id
      LEFT JOIN task_update_tags tut ON tu.id = tut.task_update_id
      LEFT JOIN task_tags tt ON tut.task_tag_id = tt.id
      WHERE tu.id = $1
      GROUP BY tu.id, p.name, tu.updated_at
    `, [taskId]);

    if (result.rows.length === 0) {
      res.status(404).json({
        success: false,
        message: 'Task not found'
      });
      return;
    }

    const task = result.rows[0];

    // Check if user owns the task or has access
    if (task.user_id !== userId && req.user!.role !== 'admin') {
      // For managers and employees, check if they're in the same department
      if (req.user!.role === 'manager' || req.user!.role === 'employee') {
        const sameDepart = await isUserInSameDepartment(userId, task.user_id);
        if (!sameDepart) {
          res.status(403).json({
            success: false,
            message: 'Access denied - not in same department'
          });
          return;
        }
      } else {
        res.status(403).json({
          success: false,
          message: 'Access denied'
        });
        return;
      }
    }

    res.json({
      success: true,
      data: task
    });
  } catch (error) {
    logger.error('Get task by ID error', {
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
      taskId: req.params.taskId,
      userId: req.user?.id
    });
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};
