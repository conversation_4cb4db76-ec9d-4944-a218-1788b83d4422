{"compilerOptions": {"target": "ES2020", "module": "commonjs", "lib": ["ES2020"], "outDir": "./dist", "rootDir": "./src", "strict": false, "esModuleInterop": true, "skipLibCheck": true, "noEmitOnError": false, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "declaration": false, "sourceMap": false, "removeComments": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "allowSyntheticDefaultImports": true, "noImplicitAny": false, "baseUrl": "./src", "paths": {"@/*": ["*"], "@/types/*": ["types/*"], "@/utils/*": ["utils/*"], "@/middleware/*": ["middleware/*"], "@/routes/*": ["routes/*"], "@/services/*": ["services/*"], "@/controllers/*": ["controllers/*"], "@/models/*": ["models/*"], "@/config/*": ["config/*"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.spec.ts", "src/test/"], "ts-node": {"require": ["tsconfig-paths/register"], "esm": true}}