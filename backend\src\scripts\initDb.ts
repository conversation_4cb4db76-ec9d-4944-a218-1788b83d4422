#!/usr/bin/env node

/**
 * Database Initialization Script
 * 
 * This script can be run manually to initialize the database tables.
 * It's also automatically called when the server starts.
 * 
 * Usage:
 *   npm run db:init
 *   or
 *   node dist/scripts/initDb.js
 */

import { config } from '@/config';
import { testConnection } from '@/config/database';
import { logger } from '@/utils/logger';
import { initializeDatabase, isDatabaseInitialized } from '@/utils/dbInit';

const runDatabaseInitialization = async () => {
  try {
    logger.info('Starting manual database initialization...');
    
    // Test database connection first
    const dbConnected = await testConnection();
    if (!dbConnected) {
      logger.error('Database connection failed. Please check your database configuration.');
      process.exit(1);
    }
    
    logger.info('Database connection successful');
    
    // Check if database is already initialized
    const isInitialized = await isDatabaseInitialized();
    
    if (isInitialized) {
      logger.info('Database is already initialized. Tables exist.');
      logger.info('If you want to recreate tables, please drop them manually first.');
      process.exit(0);
    }
    
    // Initialize database
    logger.info('Database not initialized. Creating tables...');
    await initializeDatabase();
    
    logger.info('✅ Database initialization completed successfully!');
    logger.info('The following was created:');
    logger.info('- All necessary database tables');
    logger.info('- Database indexes for performance');
    logger.info('- Database triggers for automatic updates');
    logger.info('- Content department entry');
    
    process.exit(0);
    
  } catch (error) {
    logger.error('❌ Database initialization failed:', error);
    process.exit(1);
  }
};

// Run the script if called directly
if (require.main === module) {
  runDatabaseInitialization();
}

export { runDatabaseInitialization };
