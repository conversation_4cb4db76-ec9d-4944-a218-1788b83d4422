import express, { Request, Response, NextFunction } from 'express';
import cors from 'cors';
import helmet from 'helmet';
// import compression from 'compression'; // Unused for now
import morgan from 'morgan';
import rateLimit from 'express-rate-limit';
import { config } from '@/config';
import { logger, morganStream } from '@/utils/logger';

// =====================================================
// SECURITY MIDDLEWARE
// =====================================================

// CORS configuration
export const corsMiddleware = cors({
  origin: (origin, callback) => {
    console.log('CORS Origin Check:', { origin, configOrigin: config.cors.origin });

    // Allow requests with no origin (like mobile apps or curl requests)
    if (!origin) return callback(null, true);

    // Get allowed origins from config
    const allowedOrigins = Array.isArray(config.cors.origin)
      ? config.cors.origin
      : [config.cors.origin];

    console.log('Allowed Origins:', allowedOrigins);

    // Check if the origin is allowed
    if (allowedOrigins.includes(origin)) {
      console.log('✅ Origin allowed:', origin);
      return callback(null, true);
    }

    // For development, allow localhost origins
    if (config.server.isDevelopment && origin.includes('localhost')) {
      console.log('✅ Development localhost allowed:', origin);
      return callback(null, true);
    }

    // For production, also allow Railway frontend domains
    if (config.server.isProduction && origin.includes('railway.app')) {
      console.log('✅ Railway domain allowed:', origin);
      return callback(null, true);
    }

    console.log('❌ Origin rejected:', origin);
    return callback(null, false); // Changed from Error to false
  },
  credentials: config.cors.credentials,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With', 'Origin', 'Accept'],
  exposedHeaders: ['X-Total-Count', 'X-Page-Count'],
  optionsSuccessStatus: 200 // For legacy browser support
});

// Helmet for security headers
export const helmetMiddleware = helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
      connectSrc: ["'self'"],
      fontSrc: ["'self'"],
      objectSrc: ["'none'"],
      mediaSrc: ["'self'"],
      frameSrc: ["'none'"]
    }
  },
  crossOriginEmbedderPolicy: false
});

// Rate limiting
export const rateLimitMiddleware = rateLimit({
  windowMs: config.rateLimit.windowMs,
  max: config.rateLimit.maxRequests,
  message: {
    success: false,
    message: 'Too many requests from this IP, please try again later'
  },
  standardHeaders: true,
  legacyHeaders: false,
  skip: (req) => {
    // Skip rate limiting for health checks
    return req.path === '/health' || req.path === '/api/health';
  }
});

// =====================================================
// LOGGING MIDDLEWARE
// =====================================================

// Morgan HTTP request logging
export const morganMiddleware = morgan(
  ':remote-addr - :remote-user [:date[clf]] ":method :url HTTP/:http-version" :status :res[content-length] ":referrer" ":user-agent" :response-time ms',
  {
    stream: morganStream,
    skip: (req: any, _res) => {
      // Skip logging for health checks in production
      if (config.server.isProduction && (req.path === '/health' || req.path === '/api/health')) {
        return true;
      }
      return false;
    }
  }
);

// Request ID middleware
export const requestIdMiddleware = (req: Request, res: Response, next: NextFunction) => {
  req.id = `req_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  res.setHeader('X-Request-ID', req.id);
  next();
};

// =====================================================
// PARSING MIDDLEWARE
// =====================================================

// JSON parsing with size limit
export const jsonParserMiddleware = express.json({
  limit: '10mb',
  verify: (req, _res, buf) => {
    // Store raw body for webhook verification if needed
    (req as any).rawBody = buf;
  }
});

// URL encoded parsing
export const urlEncodedMiddleware = express.urlencoded({
  extended: true,
  limit: '10mb'
});

// =====================================================
// ERROR HANDLING MIDDLEWARE
// =====================================================

// 404 handler
export const notFoundMiddleware = (req: Request, res: Response) => {
  res.status(404).json({
    success: false,
    message: `Route ${req.method} ${req.path} not found`,
    timestamp: new Date().toISOString()
  });
};

// Global error handler
export const errorHandlerMiddleware = (
  error: any,
  req: Request,
  res: Response,
  _next: NextFunction
) => {
  logger.error('Unhandled error', {
    error: error.message,
    stack: error.stack,
    url: req.url,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    userId: req.user?.id,
    requestId: req.id
  });

  // Don't leak error details in production
  const isDevelopment = config.server.isDevelopment;
  
  let statusCode = 500;
  let message = 'Internal server error';
  let details = undefined;

  // Handle specific error types
  if (error.name === 'ValidationError') {
    statusCode = 400;
    message = 'Validation error';
    details = isDevelopment ? error.details : undefined;
  } else if (error.name === 'UnauthorizedError') {
    statusCode = 401;
    message = 'Unauthorized';
  } else if (error.name === 'ForbiddenError') {
    statusCode = 403;
    message = 'Forbidden';
  } else if (error.code === '23505') { // PostgreSQL unique violation
    statusCode = 409;
    message = 'Resource already exists';
  } else if (error.code === '23503') { // PostgreSQL foreign key violation
    statusCode = 400;
    message = 'Invalid reference';
  } else if (error.code === '23502') { // PostgreSQL not null violation
    statusCode = 400;
    message = 'Required field missing';
  }

  res.status(statusCode).json({
    success: false,
    message,
    ...(isDevelopment && {
      error: error.message,
      stack: error.stack,
      details
    }),
    timestamp: new Date().toISOString(),
    requestId: req.id
  });
};

// =====================================================
// HEALTH CHECK MIDDLEWARE
// =====================================================

export const healthCheckMiddleware = async (_req: Request, res: Response) => {
  try {
    const { healthCheck } = await import('@/config/database');
    const dbHealth = await healthCheck();
    
    const health = {
      status: dbHealth.status === 'healthy' ? 'healthy' : 'unhealthy',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      services: {
        database: dbHealth
      },
      version: process.env['npm_package_version'] || '1.0.0'
    };

    const statusCode = health.status === 'healthy' ? 200 : 503;
    res.status(statusCode).json(health);
  } catch (error) {
    logger.error('Health check failed', error);
    res.status(503).json({
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: 'Health check failed'
    });
  }
};

// =====================================================
// RESPONSE HELPERS MIDDLEWARE
// =====================================================

// Add response helpers to res object
export const responseHelpersMiddleware = (req: Request, res: Response, next: NextFunction) => {
  // Success response helper
  res.success = (data?: any, message?: string, statusCode = 200) => {
    res.status(statusCode).json({
      success: true,
      data,
      message,
      timestamp: new Date().toISOString()
    });
  };

  // Error response helper
  res.error = (message: string, statusCode = 500, errors?: any[]) => {
    res.status(statusCode).json({
      success: false,
      message,
      errors,
      timestamp: new Date().toISOString(),
      requestId: req.id
    });
  };

  next();
};

// Extend Express Response interface
declare global {
  namespace Express {
    interface Request {
      id?: string;
      rawBody?: Buffer;
    }
    
    interface Response {
      success: (data?: any, message?: string, statusCode?: number) => void;
      error: (message: string, statusCode?: number, errors?: any[]) => void;
    }
  }
}
