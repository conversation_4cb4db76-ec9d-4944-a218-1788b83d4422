@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@100;200;300;400;500;600;700;800&display=swap');

@import "tailwindcss";

/* Base Styles */
* {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
  -webkit-text-size-adjust: 100%;
}

body {
  margin: 0;
  padding: 0;
  font-family: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Open Sans', 'Helvetica Neue', sans-serif;
  font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
  font-variation-settings: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
  background-color: #f9fafb;
  color: #111827;
  line-height: 1.6;
}

/* Focus styles */
*:focus {
  outline: 2px solid #6366f1;
  outline-offset: 2px;
}

*:focus:not(:focus-visible) {
  outline: none;
}

/* Custom slider styles */
.slider {
  -webkit-appearance: none;
  appearance: none;
  background: linear-gradient(to right, #3b82f6 0%, #10b981 100%);
  border-radius: 8px;
  height: 8px;
  outline: none;
}

.slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: #ffffff;
  border: 2px solid #3b82f6;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.slider::-webkit-slider-thumb:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.slider::-moz-range-thumb {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: #ffffff;
  border: 2px solid #3b82f6;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

/* Selection styles */
::selection {
  background-color: rgba(99, 102, 241, 0.2);
  color: #111827;
}

/* Scrollbar styles */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f3f4f6;
}

::-webkit-scrollbar-thumb {
  background: #d1d5db;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}

/* Component Layer - Reusable patterns */
@layer components {
  /* Modern Card Component */
  .card {
    @apply bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden;
    @apply transition-all duration-300;
  }

  .card:hover {
    @apply shadow-md border-gray-300;
  }

  .card-interactive {
    @apply cursor-pointer;
  }

  .card-interactive:hover {
    @apply shadow-lg transform scale-[1.02] border-indigo-200;
  }

  .card-interactive:active {
    @apply transform scale-[0.98];
  }

  /* Modern Button Components */
  .btn {
    @apply inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-lg;
    @apply transition-all duration-200;
    @apply focus:outline-none focus:ring-2 focus:ring-offset-2;
    @apply disabled:opacity-50 disabled:cursor-not-allowed;
  }

  .btn-primary {
    @apply bg-indigo-600 text-white hover:bg-indigo-700 focus:ring-indigo-500;
    @apply shadow-sm hover:shadow-md;
  }

  .btn-secondary {
    @apply bg-white text-gray-700 border border-gray-300 hover:bg-gray-50 focus:ring-indigo-500;
    @apply shadow-sm hover:shadow-md;
  }

  .btn-success {
    @apply bg-green-600 text-white hover:bg-green-700 focus:ring-green-500;
    @apply shadow-sm hover:shadow-md;
  }

  .btn-warning {
    @apply bg-amber-500 text-white hover:bg-amber-600 focus:ring-amber-500;
    @apply shadow-sm hover:shadow-md;
  }

  .btn-error {
    @apply bg-red-600 text-white hover:bg-red-700 focus:ring-red-500;
    @apply shadow-sm hover:shadow-md;
  }

  .btn-ghost {
    @apply text-gray-600 hover:text-gray-900 hover:bg-gray-100 focus:ring-indigo-500;
  }

  .btn-sm {
    @apply px-3 py-1.5 text-xs;
  }

  .btn-lg {
    @apply px-6 py-3 text-base;
  }

  /* Modern Input Components */
  .input {
    @apply block w-full px-3 py-2 text-sm border border-gray-300 rounded-lg;
    @apply bg-white placeholder-gray-400 text-gray-900;
    @apply transition-all duration-200;
    @apply focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500;
  }

  .input-error {
    @apply border-red-300 focus:border-red-500 focus:ring-red-500;
  }

  .input-success {
    @apply border-green-300 focus:border-green-500 focus:ring-green-500;
  }

  /* Badge Components */
  .badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }

  .badge-primary {
    @apply bg-indigo-100 text-indigo-800;
  }

  .badge-secondary {
    @apply bg-emerald-100 text-emerald-800;
  }

  .badge-success {
    @apply bg-green-100 text-green-800;
  }

  .badge-warning {
    @apply bg-amber-100 text-amber-800;
  }

  .badge-error {
    @apply bg-red-100 text-red-800;
  }

  .badge-neutral {
    @apply bg-gray-100 text-gray-800;
  }

  /* Loading States */
  .loading-spinner {
    @apply inline-block w-4 h-4 border-2 border-current border-t-transparent rounded-full;
    animation: spin 1s linear infinite;
  }

  .loading-skeleton {
    @apply bg-gray-200 rounded animate-pulse;
  }

  /* Utility Classes */
  .hover-lift {
    @apply transition-transform duration-200 hover:transform hover:-translate-y-1;
  }

  .focus-ring {
    @apply focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2;
  }

  /* Floating Header Styles */
  .floating-header {
    @apply fixed bottom-6 left-1/2 z-40;
    @apply bg-white/95 backdrop-blur-md border border-neutral-200;
    @apply rounded-full shadow-lg hover:shadow-xl;
    @apply transition-all duration-300 ease-in-out;
    @apply px-4 py-3;
    transform: translateX(-50%);
  }

  .floating-header:hover {
    transform: translateX(-50%) translateY(-4px) scale(1.02);
  }

  /* Modal Overlay Styles */
  .modal-overlay {
    @apply fixed inset-0 bg-black/50 backdrop-blur-sm z-[60];
    @apply flex items-center justify-center p-4;
  }

  .modal-content {
    @apply bg-white rounded-xl shadow-2xl border border-neutral-200;
    @apply w-full max-h-[90vh];
    @apply transform flex flex-col;
  }

  /* Animations */
  @keyframes modal-enter {
    from {
      opacity: 0;
      transform: scale(0.95) translateY(20px);
    }
    to {
      opacity: 1;
      transform: scale(1) translateY(0);
    }
  }

  @keyframes modal-exit {
    from {
      opacity: 1;
      transform: scale(1) translateY(0);
    }
    to {
      opacity: 0;
      transform: scale(0.95) translateY(20px);
    }
  }

  @keyframes fade-in {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }

  @keyframes fade-out {
    from {
      opacity: 1;
    }
    to {
      opacity: 0;
    }
  }

  .animate-fade-in {
    animation: fade-in 0.3s ease-out;
  }

  .animate-fade-out {
    animation: fade-out 0.3s ease-out;
  }

  .animate-modal-enter {
    animation: modal-enter 0.3s ease-out;
  }

  .animate-modal-exit {
    animation: modal-exit 0.3s ease-out;
  }

  @keyframes float {
    0%, 100% {
      transform: translateX(-50%) translateY(0px);
    }
    50% {
      transform: translateX(-50%) translateY(-2px);
    }
  }

  .animate-float {
    animation: float 4s ease-in-out infinite;
  }

  .animate-float:hover {
    animation-play-state: paused;
  }
}


