# Frontend directory (not needed for backend build)
frontend/

# Database files
database/

# Git
.git
.gitignore

# Documentation
README.md
*.md

# Backend build artifacts and dependencies
backend/node_modules
backend/dist
backend/logs
backend/uploads

# Frontend build artifacts and dependencies  
frontend/node_modules
frontend/dist

# Environment files
**/.env
**/.env.local
**/.env.development.local
**/.env.test.local
**/.env.production.local

# Logs
**/logs
**/*.log
**/npm-debug.log*
**/yarn-debug.log*
**/yarn-error.log*

# Runtime data
**/pids
**/*.pid
**/*.seed
**/*.pid.lock

# Coverage directory used by tools like istanbul
**/coverage

# nyc test coverage
**/.nyc_output

# Dependency directories
**/jspm_packages/

# Optional npm cache directory
**/.npm

# Optional REPL history
**/.node_repl_history

# Output of 'npm pack'
**/*.tgz

# Yarn Integrity file
**/.yarn-integrity

# IDE files
.vscode
.idea
**/*.swp
**/*.swo
**/*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Test files
**/*.test.ts
**/*.spec.ts
**/src/test/
**/__tests__/
