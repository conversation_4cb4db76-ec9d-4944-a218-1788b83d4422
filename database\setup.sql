-- Database Setup Script for Employee Task Dashboard
-- Run this script to set up the complete database

-- =====================================================
-- DATABASE CREATION (Run as superuser)
-- =====================================================

-- Create database (uncomment if creating new database)
-- CREATE DATABASE employee_task_dashboard 
--     WITH ENCODING 'UTF8' 
--     LC_COLLATE='en_US.UTF-8' 
--     LC_CTYPE='en_US.UTF-8' 
--     TEMPLATE=template0;

-- Connect to the database
-- \c employee_task_dashboard;

-- =====================================================
-- EXTENSIONS AND CONFIGURATION
-- =====================================================

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS pg_stat_statements;
CREATE EXTENSION IF NOT EXISTS pg_trgm; -- For better text search performance

-- Set timezone
SET timezone = 'UTC';

-- =====================================================
-- PERFORMANCE CONFIGURATION
-- =====================================================

-- Optimize for analytics workload
-- These settings should be adjusted based on your server specs
-- ALTER SYSTEM SET shared_buffers = '256MB';
-- ALTER SYSTEM SET effective_cache_size = '1GB';
-- ALTER SYSTEM SET maintenance_work_mem = '64MB';
-- ALTER SYSTEM SET checkpoint_completion_target = 0.9;
-- ALTER SYSTEM SET wal_buffers = '16MB';
-- ALTER SYSTEM SET default_statistics_target = 100;
-- ALTER SYSTEM SET random_page_cost = 1.1;
-- ALTER SYSTEM SET effective_io_concurrency = 200;

-- Reload configuration (requires superuser)
-- SELECT pg_reload_conf();

-- =====================================================
-- SCHEMA CREATION
-- =====================================================

-- Run the main schema file
\i schema.sql

-- =====================================================
-- INITIAL DATA SETUP
-- =====================================================

-- Load sample data for development/testing
\i migrations/001_initial_data.sql

-- =====================================================
-- ANALYTICS SETUP
-- =====================================================

-- Create materialized views for better analytics performance
CREATE MATERIALIZED VIEW mv_daily_user_productivity AS
SELECT 
    u.id as user_id,
    u.name,
    d.id as department_id,
    d.name as department_name,
    DATE(th.created_at) as date,
    COUNT(*) as status_updates,
    COUNT(CASE WHEN th.action_type = 'completed' THEN 1 END) as tasks_completed,
    AVG(th.session_duration_minutes) as avg_session_duration,
    SUM(CASE WHEN th.status = 'active' THEN th.session_duration_minutes ELSE 0 END) as active_minutes,
    SUM(th.session_duration_minutes) as total_minutes,
    COUNT(CASE WHEN th.priority = 'urgent' THEN 1 END) as urgent_tasks,
    COUNT(CASE WHEN th.priority = 'high' THEN 1 END) as high_priority_tasks
FROM task_history th
JOIN users u ON th.user_id = u.id
JOIN departments d ON u.department_id = d.id
WHERE th.session_duration_minutes IS NOT NULL
GROUP BY u.id, u.name, d.id, d.name, DATE(th.created_at);

-- Create indexes on materialized view
CREATE INDEX idx_mv_daily_productivity_user_date ON mv_daily_user_productivity(user_id, date);
CREATE INDEX idx_mv_daily_productivity_dept_date ON mv_daily_user_productivity(department_id, date);

-- Create materialized view for department metrics
CREATE MATERIALIZED VIEW mv_department_daily_metrics AS
SELECT 
    d.id as department_id,
    d.name as department_name,
    DATE(th.created_at) as date,
    COUNT(DISTINCT th.user_id) as active_users,
    COUNT(*) as total_activities,
    COUNT(CASE WHEN th.action_type = 'completed' THEN 1 END) as completed_tasks,
    AVG(th.session_duration_minutes) as avg_session_duration,
    SUM(CASE WHEN th.status = 'active' THEN th.session_duration_minutes ELSE 0 END) as total_active_minutes,
    COUNT(CASE WHEN th.priority = 'urgent' THEN 1 END) as urgent_activities,
    COUNT(CASE WHEN th.blocking_issues IS NOT NULL AND th.blocking_issues != '' THEN 1 END) as blocked_activities
FROM task_history th
JOIN users u ON th.user_id = u.id
JOIN departments d ON u.department_id = d.id
WHERE th.session_duration_minutes IS NOT NULL
GROUP BY d.id, d.name, DATE(th.created_at);

-- Create index on department metrics view
CREATE INDEX idx_mv_dept_metrics_dept_date ON mv_department_daily_metrics(department_id, date);

-- =====================================================
-- AUTOMATED REFRESH FUNCTIONS
-- =====================================================

-- Function to refresh materialized views
CREATE OR REPLACE FUNCTION refresh_analytics_views()
RETURNS void AS $$
BEGIN
    REFRESH MATERIALIZED VIEW CONCURRENTLY mv_daily_user_productivity;
    REFRESH MATERIALIZED VIEW CONCURRENTLY mv_department_daily_metrics;
    
    -- Update daily productivity summary table
    INSERT INTO daily_productivity_summary (
        user_id, department_id, date, total_active_minutes, total_idle_minutes, 
        total_offline_minutes, tasks_completed, tasks_started, status_updates_count,
        avg_task_completion_time_minutes, high_priority_tasks, urgent_priority_tasks,
        productivity_score, created_at, updated_at
    )
    SELECT 
        user_id,
        department_id,
        date,
        COALESCE(active_minutes, 0),
        0, -- idle minutes (calculated separately)
        0, -- offline minutes (calculated separately)
        COALESCE(tasks_completed, 0),
        COALESCE(status_updates, 0), -- tasks started approximation
        COALESCE(status_updates, 0),
        COALESCE(avg_session_duration, 0),
        COALESCE(high_priority_tasks, 0),
        COALESCE(urgent_tasks, 0),
        -- Simple productivity score calculation
        CASE 
            WHEN active_minutes > 0 THEN 
                LEAST(100, (tasks_completed * 20 + active_minutes / 10))
            ELSE 0 
        END,
        CURRENT_TIMESTAMP,
        CURRENT_TIMESTAMP
    FROM mv_daily_user_productivity
    WHERE date = CURRENT_DATE - INTERVAL '1 day'
    ON CONFLICT (user_id, date) DO UPDATE SET
        total_active_minutes = EXCLUDED.total_active_minutes,
        tasks_completed = EXCLUDED.tasks_completed,
        status_updates_count = EXCLUDED.status_updates_count,
        avg_task_completion_time_minutes = EXCLUDED.avg_task_completion_time_minutes,
        high_priority_tasks = EXCLUDED.high_priority_tasks,
        urgent_priority_tasks = EXCLUDED.urgent_priority_tasks,
        productivity_score = EXCLUDED.productivity_score,
        updated_at = CURRENT_TIMESTAMP;
        
    -- Update department metrics
    INSERT INTO department_metrics (
        department_id, date, total_employees, active_employees,
        total_tasks_completed, avg_productivity_score, total_active_hours,
        urgent_tasks_count, blocked_tasks_count, created_at
    )
    SELECT 
        department_id,
        date,
        active_users,
        active_users,
        COALESCE(completed_tasks, 0),
        COALESCE(avg_session_duration, 0),
        COALESCE(total_active_minutes / 60.0, 0),
        COALESCE(urgent_activities, 0),
        COALESCE(blocked_activities, 0),
        CURRENT_TIMESTAMP
    FROM mv_department_daily_metrics
    WHERE date = CURRENT_DATE - INTERVAL '1 day'
    ON CONFLICT (department_id, date) DO UPDATE SET
        total_employees = EXCLUDED.total_employees,
        active_employees = EXCLUDED.active_employees,
        total_tasks_completed = EXCLUDED.total_tasks_completed,
        avg_productivity_score = EXCLUDED.avg_productivity_score,
        total_active_hours = EXCLUDED.total_active_hours,
        urgent_tasks_count = EXCLUDED.urgent_tasks_count,
        blocked_tasks_count = EXCLUDED.blocked_tasks_count;
        
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- SCHEDULED JOBS (requires pg_cron extension)
-- =====================================================

-- Uncomment if you have pg_cron extension installed
-- Schedule daily refresh of analytics at 1 AM
-- SELECT cron.schedule('refresh-analytics', '0 1 * * *', 'SELECT refresh_analytics_views();');

-- =====================================================
-- PERMISSIONS SETUP
-- =====================================================

-- Create roles for different access levels
CREATE ROLE employee_dashboard_read;
CREATE ROLE employee_dashboard_write;
CREATE ROLE employee_dashboard_admin;

-- Grant permissions to read role
GRANT CONNECT ON DATABASE employee_task_dashboard TO employee_dashboard_read;
GRANT USAGE ON SCHEMA public TO employee_dashboard_read;
GRANT SELECT ON ALL TABLES IN SCHEMA public TO employee_dashboard_read;
GRANT SELECT ON ALL SEQUENCES IN SCHEMA public TO employee_dashboard_read;

-- Grant permissions to write role
GRANT employee_dashboard_read TO employee_dashboard_write;
GRANT INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA public TO employee_dashboard_write;
GRANT USAGE ON ALL SEQUENCES IN SCHEMA public TO employee_dashboard_write;

-- Grant permissions to admin role
GRANT employee_dashboard_write TO employee_dashboard_admin;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO employee_dashboard_admin;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO employee_dashboard_admin;

-- =====================================================
-- MONITORING SETUP
-- =====================================================

-- Create monitoring view for database health
CREATE VIEW db_health_monitor AS
SELECT 
    'active_connections' as metric,
    COUNT(*) as value,
    'connections' as unit
FROM pg_stat_activity
WHERE state = 'active'
UNION ALL
SELECT 
    'database_size' as metric,
    pg_size_pretty(pg_database_size(current_database()))::text as value,
    'bytes' as unit
UNION ALL
SELECT 
    'total_users' as metric,
    COUNT(*)::text as value,
    'users' as unit
FROM users WHERE is_active = true
UNION ALL
SELECT 
    'active_tasks' as metric,
    COUNT(*)::text as value,
    'tasks' as unit
FROM task_updates
UNION ALL
SELECT 
    'daily_updates' as metric,
    COUNT(*)::text as value,
    'updates' as unit
FROM task_history 
WHERE created_at >= CURRENT_DATE;

-- =====================================================
-- COMPLETION MESSAGE
-- =====================================================

SELECT 'Database setup completed successfully!' as status;
