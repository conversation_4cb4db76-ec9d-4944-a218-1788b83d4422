import { Request, Response } from 'express';
import { query } from '@/config/database';
import { logger } from '@/utils/logger';
import { 
  ProductivityAnalyticsResponse, 
  TaskCategoryAnalyticsResponse 
} from '@/types/api';

// =====================================================
// ANALYTICS CONTROLLERS
// =====================================================

// Get productivity analytics for user or department
export const getProductivityAnalytics = async (req: Request, res: Response): Promise<void> => {
  try {
    const { 
      user_id, 
      department_id, 
      start_date, 
      end_date, 
      period = 'daily' 
    } = req.query;

    // Validate date range
    if (!start_date || !end_date) {
      res.status(400).json({
        success: false,
        message: 'Start date and end date are required'
      });
      return;
    }

    const startDate = new Date(start_date as string);
    const endDate = new Date(end_date as string);

    if (startDate >= endDate) {
      res.status(400).json({
        success: false,
        message: 'Start date must be before end date'
      });
      return;
    }

    // Check access permissions
    if (user_id && req.user!.id !== user_id && req.user!.role !== 'admin') {
      // Managers can view their department users
      if (req.user!.role !== 'manager') {
        res.status(403).json({
          success: false,
          message: 'Access denied'
        });
        return;
      }
    }

    let whereClause = 'WHERE dps.date BETWEEN $1 AND $2';
    const params: any[] = [startDate, endDate];
    let paramCount = 2;

    if (user_id) {
      paramCount++;
      whereClause += ` AND dps.user_id = $${paramCount}`;
      params.push(user_id);
    } else if (department_id) {
      paramCount++;
      whereClause += ` AND dps.department_id = $${paramCount}`;
      params.push(department_id);
    } else if (req.user!.role !== 'admin') {
      // Non-admin users can only see their own department
      paramCount++;
      whereClause += ` AND dps.department_id = $${paramCount}`;
      params.push(req.user!.department_id);
    }

    // Determine date grouping based on period
    let dateGroup = 'dps.date';
    if (period === 'weekly') {
      dateGroup = 'DATE_TRUNC(\'week\', dps.date)';
    } else if (period === 'monthly') {
      dateGroup = 'DATE_TRUNC(\'month\', dps.date)';
    }

    const result = await query(`
      SELECT 
        ${dateGroup} as date,
        ${user_id ? 'dps.user_id, u.name as user_name,' : ''}
        ${department_id || !user_id ? 'dps.department_id, d.name as department_name,' : ''}
        SUM(dps.total_active_minutes) as active_minutes,
        SUM(dps.tasks_completed) as tasks_completed,
        AVG(dps.productivity_score) as productivity_score,
        SUM(dps.status_updates_count) as status_updates
      FROM daily_productivity_summary dps
      JOIN users u ON dps.user_id = u.id
      JOIN departments d ON dps.department_id = d.id
      ${whereClause}
      GROUP BY ${dateGroup}${user_id ? ', dps.user_id, u.name' : ''}${department_id || !user_id ? ', dps.department_id, d.name' : ''}
      ORDER BY date ASC
    `, params);

    // Calculate summary statistics
    const summaryResult = await query(`
      SELECT 
        SUM(dps.total_active_minutes) as total_active_minutes,
        SUM(dps.tasks_completed) as total_tasks_completed,
        AVG(dps.productivity_score) as avg_productivity_score
      FROM daily_productivity_summary dps
      ${whereClause}
    `, params);

    const summary = summaryResult.rows[0];
    
    // Calculate trend (simple comparison with previous period)
    const periodDays = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
    const prevStartDate = new Date(startDate.getTime() - (periodDays * 24 * 60 * 60 * 1000));
    
    const prevResult = await query(`
      SELECT AVG(dps.productivity_score) as prev_avg_score
      FROM daily_productivity_summary dps
      WHERE dps.date BETWEEN $1 AND $2
      ${user_id ? ` AND dps.user_id = $${params.length + 1}` : ''}
      ${department_id ? ` AND dps.department_id = $${params.length + 1}` : ''}
    `, [prevStartDate, startDate, ...(user_id ? [user_id] : department_id ? [department_id] : [])]);

    const currentAvg = parseFloat(summary.avg_productivity_score) || 0;
    const prevAvg = parseFloat(prevResult.rows[0]?.prev_avg_score) || 0;
    
    let trend: 'up' | 'down' | 'stable' = 'stable';
    if (currentAvg > prevAvg * 1.05) trend = 'up';
    else if (currentAvg < prevAvg * 0.95) trend = 'down';

    const data = result.rows.map(row => ({
      date: row.date.toISOString().split('T')[0],
      active_minutes: parseInt(row.active_minutes) || 0,
      tasks_completed: parseInt(row.tasks_completed) || 0,
      productivity_score: Math.round(parseFloat(row.productivity_score) || 0),
      status_updates: parseInt(row.status_updates) || 0
    }));

    const response: ProductivityAnalyticsResponse = {
      user_id: user_id as string,
      user_name: result.rows[0]?.user_name,
      department_id: department_id as string,
      department_name: result.rows[0]?.department_name,
      period: period as string,
      data,
      summary: {
        total_active_minutes: parseInt(summary.total_active_minutes) || 0,
        total_tasks_completed: parseInt(summary.total_tasks_completed) || 0,
        avg_productivity_score: Math.round(currentAvg),
        trend
      }
    };

    res.json({
      success: true,
      data: response
    });
  } catch (error) {
    logger.error('Get productivity analytics error', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

// Get task category analytics
export const getTaskCategoryAnalytics = async (req: Request, res: Response): Promise<void> => {
  try {
    const { 
      start_date, 
      end_date, 
      department_id 
    } = req.query;

    if (!start_date || !end_date) {
      res.status(400).json({
        success: false,
        message: 'Start date and end date are required'
      });
      return;
    }

    const startDate = new Date(start_date as string);
    const endDate = new Date(end_date as string);

    let whereClause = 'WHERE th.created_at BETWEEN $1 AND $2';
    const params: any[] = [startDate, endDate];
    let paramCount = 2;

    if (department_id) {
      paramCount++;
      whereClause += ` AND u.department_id = $${paramCount}`;
      params.push(department_id);
    } else if (req.user!.role !== 'admin') {
      // Non-admin users can only see their own department
      paramCount++;
      whereClause += ` AND u.department_id = $${paramCount}`;
      params.push(req.user!.department_id);
    }

    const result = await query(`
      SELECT 
        th.category,
        COUNT(*) as total_tasks,
        COUNT(CASE WHEN th.action_type = 'completed' THEN 1 END) as completed_tasks,
        ROUND(
          COUNT(CASE WHEN th.action_type = 'completed' THEN 1 END) * 100.0 / 
          NULLIF(COUNT(*), 0), 2
        ) as completion_rate,
        AVG(th.session_duration_minutes) as avg_duration_minutes
      FROM task_history th
      JOIN users u ON th.user_id = u.id
      ${whereClause}
      GROUP BY th.category
      ORDER BY total_tasks DESC
    `, params);

    // Calculate trends (compare with previous period)
    const periodDays = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
    const prevStartDate = new Date(startDate.getTime() - (periodDays * 24 * 60 * 60 * 1000));
    
    const prevResult = await query(`
      SELECT 
        th.category,
        ROUND(
          COUNT(CASE WHEN th.action_type = 'completed' THEN 1 END) * 100.0 / 
          NULLIF(COUNT(*), 0), 2
        ) as prev_completion_rate
      FROM task_history th
      JOIN users u ON th.user_id = u.id
      WHERE th.created_at BETWEEN $1 AND $2
      ${department_id ? ` AND u.department_id = $${params.length}` : req.user!.role !== 'admin' ? ` AND u.department_id = $${params.length}` : ''}
      GROUP BY th.category
    `, [
      prevStartDate, 
      startDate, 
      ...(department_id ? [department_id] : req.user!.role !== 'admin' ? [req.user!.department_id] : [])
    ]);

    const prevRates = new Map(
      prevResult.rows.map(row => [row.category, parseFloat(row.prev_completion_rate) || 0])
    );

    const categories = result.rows.map(row => {
      const currentRate = parseFloat(row.completion_rate) || 0;
      const prevRate = parseFloat(prevRates.get(row.category) as string) || 0;

      let trend: 'up' | 'down' | 'stable' = 'stable';
      if (currentRate > prevRate * 1.05) trend = 'up';
      else if (currentRate < prevRate * 0.95) trend = 'down';

      return {
        category: row.category,
        total_tasks: parseInt(row.total_tasks),
        completed_tasks: parseInt(row.completed_tasks),
        completion_rate: currentRate,
        avg_duration_minutes: Math.round(parseFloat(row.avg_duration_minutes) || 0),
        trend
      };
    });

    const response: TaskCategoryAnalyticsResponse = {
      categories,
      period: {
        start_date: startDate.toISOString().split('T')[0],
        end_date: endDate.toISOString().split('T')[0]
      }
    };

    res.json({
      success: true,
      data: response
    });
  } catch (error) {
    logger.error('Get task category analytics error', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

// Get department comparison analytics
export const getDepartmentComparison = async (req: Request, res: Response): Promise<void> => {
  try {
    const { start_date, end_date } = req.query;

    if (!start_date || !end_date) {
      res.status(400).json({
        success: false,
        message: 'Start date and end date are required'
      });
      return;
    }

    // Only admins can view all department comparisons
    if (req.user!.role !== 'admin') {
      res.status(403).json({
        success: false,
        message: 'Admin access required'
      });
      return;
    }

    const result = await query(`
      SELECT 
        d.id,
        d.name,
        COUNT(DISTINCT dps.user_id) as active_users,
        AVG(dps.productivity_score) as avg_productivity_score,
        SUM(dps.tasks_completed) as total_tasks_completed,
        SUM(dps.total_active_minutes) / 60.0 as total_active_hours,
        ROUND(
          SUM(dps.tasks_completed) * 100.0 / 
          NULLIF(SUM(dps.status_updates_count), 0), 2
        ) as completion_rate
      FROM departments d
      LEFT JOIN daily_productivity_summary dps ON d.id = dps.department_id
        AND dps.date BETWEEN $1 AND $2
      WHERE d.is_active = true
      GROUP BY d.id, d.name
      ORDER BY avg_productivity_score DESC NULLS LAST
    `, [start_date, end_date]);

    const departments = result.rows.map(row => ({
      id: row.id,
      name: row.name,
      active_users: parseInt(row.active_users) || 0,
      avg_productivity_score: Math.round(parseFloat(row.avg_productivity_score) || 0),
      total_tasks_completed: parseInt(row.total_tasks_completed) || 0,
      total_active_hours: Math.round(parseFloat(row.total_active_hours) || 0),
      completion_rate: parseFloat(row.completion_rate) || 0
    }));

    res.json({
      success: true,
      data: {
        departments,
        period: {
          start_date: start_date as string,
          end_date: end_date as string
        }
      }
    });
  } catch (error) {
    logger.error('Get department comparison error', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

// Get real-time analytics summary
export const getRealtimeAnalytics = async (req: Request, res: Response): Promise<void> => {
  try {
    // Get current day statistics
    const today = new Date().toISOString().split('T')[0];
    
    let whereClause = '';
    const params: any[] = [today];
    
    if (req.user!.role !== 'admin') {
      whereClause = 'AND u.department_id = $2';
      params.push(req.user!.department_id);
    }

    const result = await query(`
      SELECT 
        COUNT(DISTINCT tu.user_id) as active_users,
        COUNT(CASE WHEN tu.status = 'active' THEN 1 END) as currently_active,
        COUNT(CASE WHEN tu.priority = 'urgent' THEN 1 END) as urgent_tasks,
        COUNT(CASE WHEN tu.blocking_issues IS NOT NULL AND tu.blocking_issues != '' THEN 1 END) as blocked_tasks,
        AVG(tu.progress_percentage) as avg_progress,
        COUNT(CASE WHEN th.action_type = 'completed' AND DATE(th.created_at) = $1 THEN 1 END) as tasks_completed_today
      FROM task_updates tu
      JOIN users u ON tu.user_id = u.id
      LEFT JOIN task_history th ON tu.user_id = th.user_id
      WHERE u.is_active = true ${whereClause}
    `, params);

    const stats = result.rows[0];

    res.json({
      success: true,
      data: {
        active_users: parseInt(stats.active_users) || 0,
        currently_active: parseInt(stats.currently_active) || 0,
        urgent_tasks: parseInt(stats.urgent_tasks) || 0,
        blocked_tasks: parseInt(stats.blocked_tasks) || 0,
        avg_progress: Math.round(parseFloat(stats.avg_progress) || 0),
        tasks_completed_today: parseInt(stats.tasks_completed_today) || 0,
        timestamp: new Date().toISOString()
      }
    });
  } catch (error) {
    logger.error('Get realtime analytics error', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};
