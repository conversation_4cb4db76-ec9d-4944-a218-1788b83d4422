import { Request, Response, NextFunction } from 'express';
import { verifyAccessToken, extractTokenFromHeader, TokenPayload } from '@/utils/auth';
import { query } from '@/config/database';
import { User } from '@/types/database';
import { logger, logAuth } from '@/utils/logger';

// Extend Express Request interface to include user
declare global {
  namespace Express {
    interface Request {
      user?: User;
      tokenPayload?: TokenPayload;
    }
  }
}

// =====================================================
// AUTHENTICATION MIDDLEWARE
// =====================================================

// Verify JWT token and attach user to request
export const authenticateToken = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const authHeader = req.headers.authorization;
    const token = extractTokenFromHeader(authHeader);

    if (!token) {
      res.status(401).json({
        success: false,
        message: 'Access token required'
      });
      return;
    }

    // Verify token
    const payload = verifyAccessToken(token);
    if (!payload) {
      res.status(401).json({
        success: false,
        message: 'Invalid or expired token'
      });
      return;
    }

    // Fetch user from database
    const result = await query(
      'SELECT * FROM users WHERE id = $1 AND is_active = true',
      [payload.userId]
    );

    if (result.rows.length === 0) {
      logAuth('user_not_found', payload.userId, { token: 'access' });
      res.status(401).json({
        success: false,
        message: 'User not found or inactive'
      });
      return;
    }

    const user = result.rows[0] as User;

    // Update last login time
    await query(
      'UPDATE users SET last_login_at = CURRENT_TIMESTAMP WHERE id = $1',
      [user.id]
    );

    // Attach user and payload to request
    req.user = user;
    req.tokenPayload = payload;

    logAuth('token_verified', user.id, {
      email: user.email,
      role: user.role
    });

    next();
  } catch (error) {
    logger.error('Authentication middleware error', error);
    res.status(500).json({
      success: false,
      message: 'Authentication error'
    });
  }
};

// Optional authentication - doesn't fail if no token
export const optionalAuth = async (
  req: Request,
  _res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const authHeader = req.headers.authorization;
    const token = extractTokenFromHeader(authHeader);

    if (token) {
      const payload = verifyAccessToken(token);
      if (payload) {
        const result = await query(
          'SELECT * FROM users WHERE id = $1 AND is_active = true',
          [payload.userId]
        );

        if (result.rows.length > 0) {
          req.user = result.rows[0] as User;
          req.tokenPayload = payload;
        }
      }
    }

    next();
  } catch (error) {
    // Don't fail on optional auth errors
    logger.warn('Optional authentication error', error);
    next();
  }
};

// =====================================================
// AUTHORIZATION MIDDLEWARE
// =====================================================

// Require specific role
export const requireRole = (requiredRole: string) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    if (!req.user) {
      res.status(401).json({
        success: false,
        message: 'Authentication required'
      });
      return;
    }

    const roleHierarchy = {
      'admin': 3,
      'manager': 2,
      'employee': 1
    };

    const userLevel = roleHierarchy[req.user.role as keyof typeof roleHierarchy] || 0;
    const requiredLevel = roleHierarchy[requiredRole as keyof typeof roleHierarchy] || 0;

    if (userLevel < requiredLevel) {
      logAuth('insufficient_permissions', req.user.id, {
        userRole: req.user.role,
        requiredRole,
        endpoint: req.path
      });

      res.status(403).json({
        success: false,
        message: 'Insufficient permissions'
      });
      return;
    }

    next();
  };
};

// Require admin role
export const requireAdmin = requireRole('admin');

// Require manager or admin role
export const requireManager = requireRole('manager');

// =====================================================
// DEPARTMENT ACCESS MIDDLEWARE
// =====================================================

// Check department access
export const requireDepartmentAccess = (
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  if (!req.user) {
    res.status(401).json({
      success: false,
      message: 'Authentication required'
    });
    return;
  }

  const departmentId = req.params['id'] || req.params['departmentId'] || req.body.department_id;
  
  if (!departmentId) {
    res.status(400).json({
      success: false,
      message: 'Department ID required'
    });
    return;
  }

  // Admins can access all departments
  if (req.user.role === 'admin') {
    next();
    return;
  }

  // Users can only access their own department
  if (req.user.department_id !== departmentId) {
    logAuth('department_access_denied', req.user.id, {
      userDepartment: req.user.department_id,
      requestedDepartment: departmentId
    });

    res.status(403).json({
      success: false,
      message: 'Access denied to this department'
    });
    return;
  }

  next();
};

// =====================================================
// USER ACCESS MIDDLEWARE
// =====================================================

// Check if user can access/modify another user's data
export const requireUserAccess = (
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  if (!req.user) {
    res.status(401).json({
      success: false,
      message: 'Authentication required'
    });
    return;
  }

  const targetUserId = req.params['userId'] || req.params['id'];

  if (!targetUserId) {
    res.status(400).json({
      success: false,
      message: 'User ID required'
    });
    return;
  }

  // Enhanced logging for debugging
  logger.info('requireUserAccess middleware check', {
    requestingUserId: req.user.id,
    requestingUserRole: req.user.role,
    requestingUserEmail: req.user.email,
    targetUserId,
    endpoint: req.path
  });

  // Users can access their own data
  if (req.user.id === targetUserId) {
    logger.info('User accessing own data - allowed');
    next();
    return;
  }

  // Admins can access all users
  if (req.user.role === 'admin') {
    logger.info('Admin accessing user data - allowed');
    next();
    return;
  }

  // Managers can access users in their department (check at route level)
  if (req.user.role === 'manager') {
    logger.info('Manager accessing user data - allowed');
    next();
    return;
  }

  // Allow employees to view other employees in the same department
  // This enables the employee details modal functionality
  if (req.user.role === 'employee') {
    logger.info('Employee accessing user data - allowing for department colleagues', {
      requestingUserId: req.user.id,
      targetUserId,
      requestingUserDept: req.user.department_id
    });
    next();
    return;
  }

  logger.warn('User access denied in requireUserAccess middleware', {
    requestingUserId: req.user.id,
    requestingUserRole: req.user.role,
    targetUserId,
    reason: 'Not admin, manager, employee, or self'
  });

  logAuth('user_access_denied', req.user.id, {
    targetUserId,
    userRole: req.user.role
  });

  res.status(403).json({
    success: false,
    message: 'Access denied to this user data'
  });
};

// =====================================================
// TASK ACCESS MIDDLEWARE
// =====================================================

// Check if user can modify task
export const requireTaskAccess = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    if (!req.user) {
      res.status(401).json({
        success: false,
        message: 'Authentication required'
      });
      return;
    }

    const taskId = req.params['taskId'];
    
    if (!taskId) {
      res.status(400).json({
        success: false,
        message: 'Task ID required'
      });
      return;
    }

    // Get task owner
    const result = await query(
      'SELECT user_id FROM task_updates WHERE id = $1',
      [taskId]
    );

    if (result.rows.length === 0) {
      res.status(404).json({
        success: false,
        message: 'Task not found'
      });
      return;
    }

    const taskOwnerId = result.rows[0].user_id;

    // Users can modify their own tasks
    if (req.user.id === taskOwnerId) {
      next();
      return;
    }

    // Admins can modify all tasks
    if (req.user.role === 'admin') {
      next();
      return;
    }

    // Managers can modify tasks in their department
    if (req.user.role === 'manager') {
      const userResult = await query(
        'SELECT department_id FROM users WHERE id = $1',
        [taskOwnerId]
      );

      if (userResult.rows.length > 0 && 
          userResult.rows[0].department_id === req.user.department_id) {
        next();
        return;
      }
    }

    logAuth('task_access_denied', req.user.id, {
      taskId,
      taskOwnerId,
      userRole: req.user.role
    });

    res.status(403).json({
      success: false,
      message: 'Access denied to this task'
    });
  } catch (error) {
    logger.error('Task access middleware error', error);
    res.status(500).json({
      success: false,
      message: 'Authorization error'
    });
  }
};
