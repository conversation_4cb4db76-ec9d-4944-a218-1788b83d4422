import { Router } from 'express';
import { body, validationResult } from 'express-validator';
import rateLimit from 'express-rate-limit';
import {
  login,
  signup,
  refreshToken,
  changePassword,
  logout,
  getProfile,
  getPublicDepartments
} from '@/controllers/authController';
import { authenticateToken } from '@/middleware/auth';

const router = Router();

// =====================================================
// RATE LIMITING
// =====================================================

// Strict rate limiting for auth endpoints
const authRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 5, // 5 attempts per window
  message: {
    success: false,
    message: 'Too many authentication attempts, please try again later'
  },
  standardHeaders: true,
  legacyHeaders: false
});

// More lenient rate limiting for token refresh
const refreshRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 20, // 20 refreshes per window
  message: {
    success: false,
    message: 'Too many token refresh attempts, please try again later'
  },
  standardHeaders: true,
  legacyHeaders: false
});

// =====================================================
// VALIDATION RULES
// =====================================================

const loginValidation = [
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Valid email is required'),
  body('password')
    .isLength({ min: 1 })
    .withMessage('Password is required')
];

const signupValidation = [
  body('name')
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Name must be between 2 and 100 characters'),
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Valid email is required')
    .custom((value) => {
      if (!value.toLowerCase().endsWith('@imocha.io')) {
        throw new Error('Only @imocha.io email addresses are allowed');
      }
      return true;
    }),
  body('password')
    .isLength({ min: 8, max: 128 })
    .withMessage('Password must be between 8 and 128 characters')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?])/)
    .withMessage('Password must contain at least one lowercase letter, one uppercase letter, one number, and one special character'),
  body('department_id')
    .isUUID()
    .withMessage('Valid department ID is required'),
  body('phone')
    .optional()
    .isMobilePhone('any')
    .withMessage('Valid phone number is required'),
  body('timezone')
    .optional()
    .isLength({ min: 1, max: 50 })
    .withMessage('Valid timezone is required')
];

const changePasswordValidation = [
  body('currentPassword')
    .isLength({ min: 1 })
    .withMessage('Current password is required'),
  body('newPassword')
    .isLength({ min: 8, max: 128 })
    .withMessage('New password must be between 8 and 128 characters')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?])/)
    .withMessage('New password must contain at least one lowercase letter, one uppercase letter, one number, and one special character')
];

const refreshTokenValidation = [
  body('refreshToken')
    .isLength({ min: 1 })
    .withMessage('Refresh token is required')
];

// =====================================================
// VALIDATION MIDDLEWARE
// =====================================================

const handleValidationErrors = (req: any, res: any, next: any) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      message: 'Validation failed',
      errors: errors.array().map(error => ({
        field: (error as any).path || (error as any).param || 'unknown',
        message: error.msg,
        code: 'VALIDATION_ERROR'
      }))
    });
  }
  next();
};

// =====================================================
// ROUTES
// =====================================================

/**
 * @route   POST /api/auth/login
 * @desc    Authenticate user and return JWT token
 * @access  Public
 * @body    { email: string, password: string }
 */
router.post('/login', 
  authRateLimit,
  loginValidation,
  handleValidationErrors,
  login
);

/**
 * @route   POST /api/auth/signup
 * @desc    Register new user
 * @access  Public
 * @body    { name: string, email: string, password: string, department_id: string, phone?: string, timezone?: string }
 */
router.post('/signup',
  authRateLimit,
  signupValidation,
  handleValidationErrors,
  signup
);

/**
 * @route   POST /api/auth/refresh
 * @desc    Refresh access token using refresh token
 * @access  Public
 * @body    { refreshToken: string }
 */
router.post('/refresh',
  refreshRateLimit,
  refreshTokenValidation,
  handleValidationErrors,
  refreshToken
);

/**
 * @route   POST /api/auth/change-password
 * @desc    Change user password
 * @access  Private
 * @body    { currentPassword: string, newPassword: string }
 */
router.post('/change-password',
  authenticateToken,
  changePasswordValidation,
  handleValidationErrors,
  changePassword
);

/**
 * @route   POST /api/auth/logout
 * @desc    Logout user (client-side token invalidation)
 * @access  Private
 */
router.post('/logout',
  authenticateToken,
  logout
);

/**
 * @route   GET /api/auth/profile
 * @desc    Get current user profile
 * @access  Private
 */
router.get('/profile',
  authenticateToken,
  getProfile
);

/**
 * @route   GET /api/auth/verify
 * @desc    Verify token validity
 * @access  Private
 */
router.get('/verify',
  authenticateToken,
  (req, res) => {
    res.json({
      success: true,
      data: {
        valid: true,
        user: {
          id: req.user!.id,
          name: req.user!.name,
          email: req.user!.email,
          role: req.user!.role,
          department_id: req.user!.department_id
        }
      },
      message: 'Token is valid'
    });
  }
);

/**
 * @route   GET /api/auth/departments
 * @desc    Get departments for signup form
 * @access  Public
 * @returns Array of departments with id, name, and description
 */
router.get('/departments', getPublicDepartments);

export default router;
