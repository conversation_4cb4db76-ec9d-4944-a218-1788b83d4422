import { Router } from 'express';
import { body, param, query, validationResult } from 'express-validator';
import {
  getCurrentTask,
  updateTask,
  getTaskHistory,
  getTags,
  completeTask,
  deleteTask,
  getTaskById
} from '@/controllers/taskController';
import { authenticateToken, requireUserAccess } from '@/middleware/auth';
import { logger } from '@/utils/logger';

const router = Router();

// =====================================================
// VALIDATION RULES
// =====================================================

const userIdValidation = [
  param('userId')
    .isUUID()
    .withMessage('Valid user ID is required')
];

const taskUpdateValidation = [
  body('task_description')
    .trim()
    .isLength({ min: 5, max: 1000 })
    .withMessage('Task description must be between 5 and 1000 characters'),
  body('status')
    .isIn(['active', 'idle', 'offline'])
    .withMessage('Status must be active, idle, or offline'),
  body('priority')
    .isIn(['low', 'medium', 'high', 'urgent'])
    .withMessage('Priority must be low, medium, high, or urgent'),
  body('category')
    .isIn(['question-creation', 'project-delivery', 'uploading', 'quality-checking'])
    .withMessage('Category must be a valid task category'),
  body('estimated_duration_minutes')
    .optional()
    .isInt({ min: 1, max: 10080 }) // Max 1 week in minutes
    .withMessage('Estimated duration must be between 1 and 10080 minutes'),
  body('progress_percentage')
    .isInt({ min: 0, max: 100 })
    .withMessage('Progress percentage must be between 0 and 100'),
  body('project_id')
    .optional()
    .isUUID()
    .withMessage('Project ID must be a valid UUID'),
  body('expected_completion_date')
    .optional()
    .isISO8601()
    .withMessage('Expected completion date must be a valid ISO 8601 date'),
  body('blocking_issues')
    .optional()
    .isLength({ max: 500 })
    .withMessage('Blocking issues must be less than 500 characters'),
  body('tags')
    .optional()
    .isArray({ max: 10 })
    .withMessage('Tags must be an array with maximum 10 items'),
  body('tags.*')
    .optional()
    .isLength({ min: 1, max: 50 })
    .withMessage('Each tag must be between 1 and 50 characters'),
  body('number_of_questions')
    .optional()
    .isInt({ min: 1, max: 10000 })
    .withMessage('Number of questions must be between 1 and 10000'),
  body('expected_finish_datetime')
    .optional()
    .isISO8601()
    .withMessage('Expected finish datetime must be a valid ISO 8601 datetime')
];

const historyQueryValidation = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100')
];

const tagSearchValidation = [
  query('search')
    .optional()
    .isLength({ min: 1, max: 50 })
    .withMessage('Search term must be between 1 and 50 characters')
];

// =====================================================
// VALIDATION MIDDLEWARE
// =====================================================

const handleValidationErrors = (req: any, res: any, next: any) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    logger.debug('Validation errors:', {
      errors: errors.array(),
      body: req.body,
      userId: req.user?.id
    });
    return res.status(400).json({
      success: false,
      message: 'Validation failed',
      errors: errors.array().map(error => ({
        field: (error as any).path || (error as any).param || 'unknown',
        message: error.msg,
        code: 'VALIDATION_ERROR'
      }))
    });
  }
  next();
};

// =====================================================
// ROUTES
// =====================================================

/**
 * @route   GET /api/tasks/current
 * @desc    Get current task for authenticated user
 * @access  Private
 * @returns Current task details with tags and project info
 */
router.get('/current',
  authenticateToken,
  (req: any, _res: any, next: any) => {
    // Set userId to current user for getCurrentTask
    req.params['userId'] = req.user!.id;
    next();
  },
  getCurrentTask
);

/**
 * @route   GET /api/tasks/users/:userId/current
 * @desc    Get current task for specific user
 * @access  Private (user access required)
 * @param   userId - User UUID
 * @returns Current task details with tags and project info
 */
router.get('/users/:userId/current',
  authenticateToken,
  userIdValidation,
  handleValidationErrors,
  requireUserAccess,
  getCurrentTask
);

/**
 * @route   POST /api/tasks/update
 * @desc    Update or create task for authenticated user
 * @access  Private
 * @body    TaskUpdateRequest
 * @returns Updated task details
 */
router.post('/update',
  authenticateToken,
  taskUpdateValidation,
  handleValidationErrors,
  updateTask
);

/**
 * @route   DELETE /api/tasks/:taskId
 * @desc    Delete/discard a task
 * @access  Private (only task owner or admin)
 * @param   taskId - Task UUID
 * @returns Success confirmation
 */
router.delete('/:taskId',
  authenticateToken,
  param('taskId').isUUID().withMessage('Invalid task ID'),
  handleValidationErrors,
  deleteTask
);

/**
 * @route   GET /api/tasks/:taskId
 * @desc    Get specific task details
 * @access  Private (only task owner or admin)
 * @param   taskId - Task UUID
 * @returns Task details with tags and project info
 */
router.get('/:taskId',
  authenticateToken,
  param('taskId').isUUID().withMessage('Invalid task ID'),
  handleValidationErrors,
  getTaskById
);

/**
 * @route   GET /api/tasks/users/:userId/history
 * @desc    Get task history for specific user
 * @access  Private (user access required)
 * @param   userId - User UUID
 * @query   page - Page number (default: 1)
 * @query   limit - Items per page (default: 20, max: 100)
 * @returns Paginated task history
 */
router.get('/users/:userId/history',
  authenticateToken,
  userIdValidation,
  historyQueryValidation,
  handleValidationErrors,
  requireUserAccess,
  getTaskHistory
);

/**
 * @route   GET /api/tasks/history
 * @desc    Get task history for authenticated user
 * @access  Private
 * @query   page - Page number (default: 1)
 * @query   limit - Items per page (default: 20, max: 100)
 * @returns Paginated task history
 */
router.get('/history',
  authenticateToken,
  historyQueryValidation,
  handleValidationErrors,
  (req: any, _res: any, next: any) => {
    // Set userId to current user for getTaskHistory
    req.params['userId'] = req.user!.id;
    next();
  },
  getTaskHistory
);

/**
 * @route   GET /api/tasks/tags
 * @desc    Get available task tags
 * @access  Private
 * @query   search - Search term for tag names
 * @returns Array of available tags with usage counts
 */
router.get('/tags',
  authenticateToken,
  tagSearchValidation,
  handleValidationErrors,
  getTags
);

/**
 * @route   POST /api/tasks/:taskId/complete
 * @desc    Complete current task
 * @access  Private
 * @param   taskId - Task UUID
 * @returns Success message
 */
router.post('/:taskId/complete',
  authenticateToken,
  param('taskId')
    .isUUID()
    .withMessage('Valid task ID is required'),
  handleValidationErrors,
  completeTask
);

/**
 * @route   GET /api/tasks/debug/user-info
 * @desc    Debug endpoint to check user authentication and info
 * @access  Private
 * @returns Current user information
 */
router.get('/debug/user-info',
  authenticateToken,
  (req, res) => {
    res.json({
      success: true,
      data: {
        user: {
          id: req.user!.id,
          name: req.user!.name,
          email: req.user!.email,
          role: req.user!.role,
          department_id: req.user!.department_id
        },
        timestamp: new Date().toISOString()
      },
      message: 'User info retrieved successfully'
    });
  }
);

/**
 * @route   GET /api/tasks/debug/access-test/:userId
 * @desc    Debug endpoint to test access control step by step
 * @access  Private
 * @returns Access control test results
 */
router.get('/debug/access-test/:userId',
  authenticateToken,
  (req, res) => {
    const { userId } = req.params;

    const result = {
      success: true,
      data: {
        requestingUser: {
          id: req.user!.id,
          role: req.user!.role,
          department_id: req.user!.department_id
        },
        targetUserId: userId,
        accessChecks: {
          isAuthenticated: !!req.user,
          isSelf: req.user!.id === userId,
          isAdmin: req.user!.role === 'admin',
          isManager: req.user!.role === 'manager',
          userIdIsUUID: userId ? /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(userId) : false
        },
        timestamp: new Date().toISOString()
      },
      message: 'Access test completed'
    };

    res.json(result);
  }
);

export default router;
