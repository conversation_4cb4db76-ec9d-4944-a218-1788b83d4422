import React, { useState, useEffect } from 'react';
import { cn } from '../../utils/cn';
import { getCurrentTime12Hour, getCurrentTime12HourWithSeconds } from '../../utils/time';

export interface ClockProps extends React.HTMLAttributes<HTMLDivElement> {
  showSeconds?: boolean;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'default' | 'minimal' | 'badge';
}

const Clock = React.forwardRef<HTMLDivElement, ClockProps>(
  ({ className, showSeconds = false, size = 'md', variant = 'default', ...props }, ref) => {
    const [currentTime, setCurrentTime] = useState<string>('');

    useEffect(() => {
      const updateTime = () => {
        const time = showSeconds ? getCurrentTime12HourWithSeconds() : getCurrentTime12Hour();
        setCurrentTime(time);
      };

      // Update immediately
      updateTime();

      // Set up interval to update every second if showing seconds, otherwise every minute
      const interval = setInterval(updateTime, showSeconds ? 1000 : 60000);

      return () => clearInterval(interval);
    }, [showSeconds]);

    const sizeClasses = {
      sm: 'text-xs',
      md: 'text-sm',
      lg: 'text-base'
    };

    const variantClasses = {
      default: 'text-neutral-700 font-medium',
      minimal: 'text-neutral-600 font-normal',
      badge: 'bg-neutral-100 text-neutral-800 px-2 py-1 rounded-md font-medium'
    };

    return (
      <div
        ref={ref}
        className={cn(
          'clock inline-flex items-center',
          sizeClasses[size],
          variantClasses[variant],
          className
        )}
        title={`Current time: ${currentTime}`}
        {...props}
      >
        <svg 
          className="w-4 h-4 mr-1.5 text-neutral-500" 
          fill="none" 
          stroke="currentColor" 
          viewBox="0 0 24 24"
          aria-hidden="true"
        >
          <path 
            strokeLinecap="round" 
            strokeLinejoin="round" 
            strokeWidth={2} 
            d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" 
          />
        </svg>
        <span className="tabular-nums">
          {currentTime || (showSeconds ? getCurrentTime12HourWithSeconds() : getCurrentTime12Hour())}
        </span>
      </div>
    );
  }
);

Clock.displayName = 'Clock';

export default Clock;
