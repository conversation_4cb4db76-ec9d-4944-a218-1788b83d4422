import dotenv from 'dotenv';
import path from 'path';

// Load environment variables
dotenv.config();

// Validate required environment variables with Railway support
const requiredEnvVars = [
  'NODE_ENV',
  'PORT',
  'JWT_SECRET'
];

// Database configuration - Railway PostgreSQL uses DATABASE_URL
const databaseUrl = process.env['DATABASE_URL'];
if (!databaseUrl) {
  console.error('❌ Missing DATABASE_URL environment variable');
  console.error('Available env vars:', Object.keys(process.env).filter(key =>
    key.includes('DB_') || key.includes('PG') || key.includes('DATABASE')
  ));
  throw new Error('DATABASE_URL is required. Check Railway PostgreSQL addon configuration.');
}

console.log('✅ Using DATABASE_URL for database configuration');

for (const envVar of requiredEnvVars) {
  if (!process.env[envVar]) {
    throw new Error(`Missing required environment variable: ${envVar}`);
  }
}

export const config = {
  // Server configuration
  server: {
    env: process.env['NODE_ENV'] || 'development',
    port: parseInt(process.env['PORT'] || '3001', 10),
    host: process.env['HOST'] || (process.env['NODE_ENV'] === 'production' ? '0.0.0.0' : 'localhost'),
    isDevelopment: process.env['NODE_ENV'] === 'development',
    isProduction: process.env['NODE_ENV'] === 'production',
    isTest: process.env['NODE_ENV'] === 'test'
  },

  // Database configuration - Railway uses DATABASE_URL
  db: {
    connectionString: databaseUrl,
    ssl: process.env['DB_SSL'] === 'true' || process.env['NODE_ENV'] === 'production',
    maxConnections: parseInt(process.env['DB_MAX_CONNECTIONS'] || '20', 10)
  },

  // JWT configuration
  jwt: {
    secret: process.env['JWT_SECRET']!,
    expiresIn: process.env['JWT_EXPIRES_IN'] || '24h',
    refreshSecret: process.env['JWT_REFRESH_SECRET'] || process.env['JWT_SECRET']!,
    refreshExpiresIn: process.env['JWT_REFRESH_EXPIRES_IN'] || '7d'
  },

  // Redis configuration
  redis: {
    host: process.env['REDIS_HOST'] || (process.env['NODE_ENV'] === 'production' ? undefined : 'localhost'),
    port: parseInt(process.env['REDIS_PORT'] || '6379', 10),
    password: process.env['REDIS_PASSWORD'] || undefined,
    db: parseInt(process.env['REDIS_DB'] || '0', 10),
    enabled: !!process.env['REDIS_HOST'] // Only enable if Redis host is provided
  },

  // CORS configuration
  cors: {
    origin: process.env['CORS_ORIGIN']
      ? process.env['CORS_ORIGIN'].split(',').map(origin => origin.trim())
      : (process.env['NODE_ENV'] === 'production'
          ? ['https://employee-task-2-production.up.railway.app']
          : ['http://localhost:5173', 'http://localhost:3000']),
    credentials: process.env['CORS_CREDENTIALS'] === 'true' || process.env['NODE_ENV'] === 'production'
  },

  // Rate limiting
  rateLimit: {
    windowMs: parseInt(process.env['RATE_LIMIT_WINDOW_MS'] || '900000', 10), // 15 minutes
    maxRequests: parseInt(process.env['RATE_LIMIT_MAX_REQUESTS'] || '100', 10)
  },

  // File upload
  upload: {
    maxFileSize: parseInt(process.env['MAX_FILE_SIZE'] || '5242880', 10), // 5MB
    uploadPath: process.env['UPLOAD_PATH'] || path.join(process.cwd(), 'uploads')
  },

  // Logging
  logging: {
    level: process.env['LOG_LEVEL'] || 'info',
    file: process.env['LOG_FILE'] || path.join(process.cwd(), 'logs/app.log'),
    enableMorgan: process.env['ENABLE_MORGAN_LOGGING'] === 'true'
  },

  // Email configuration
  email: {
    host: process.env['SMTP_HOST'] || 'smtp.gmail.com',
    port: parseInt(process.env['SMTP_PORT'] || '587', 10),
    user: process.env['SMTP_USER'] || '',
    password: process.env['SMTP_PASSWORD'] || '',
    from: process.env['SMTP_FROM'] || '<EMAIL>'
  },

  // WebSocket configuration
  websocket: {
    corsOrigin: process.env['WS_CORS_ORIGIN']
      ? process.env['WS_CORS_ORIGIN'].split(',').map(origin => origin.trim())
      : (process.env['NODE_ENV'] === 'production'
          ? ['https://employee-task-2-production.up.railway.app']
          : ['http://localhost:5173', 'http://localhost:3000'])
  },

  // Analytics configuration
  analytics: {
    refreshInterval: parseInt(process.env['ANALYTICS_REFRESH_INTERVAL'] || '3600000', 10), // 1 hour
    enableRealTime: process.env['ENABLE_REAL_TIME_ANALYTICS'] === 'true'
  },

  // Security
  security: {
    bcryptRounds: parseInt(process.env['BCRYPT_ROUNDS'] || '12', 10),
    sessionSecret: process.env['SESSION_SECRET'] || process.env['JWT_SECRET']!
  },

  // Development features
  development: {
    enableSwagger: process.env['ENABLE_SWAGGER'] === 'true',
    enableDetailedErrors: process.env['ENABLE_DETAILED_ERRORS'] === 'true'
  },

  // Paths
  paths: {
    root: path.resolve(__dirname, '../..'),
    src: path.resolve(__dirname, '..'),
    uploads: path.resolve(__dirname, '../../uploads'),
    logs: path.resolve(__dirname, '../../logs')
  }
};

// Validate configuration
export const validateConfig = (): void => {
  // Validate port range
  if (config.server.port < 1 || config.server.port > 65535) {
    throw new Error('PORT must be between 1 and 65535');
  }

  // Validate JWT secret length
  if (config.jwt.secret.length < 32) {
    throw new Error('JWT_SECRET must be at least 32 characters long');
  }

  // Validate bcrypt rounds
  if (config.security.bcryptRounds < 10 || config.security.bcryptRounds > 15) {
    throw new Error('BCRYPT_ROUNDS must be between 10 and 15');
  }
};

// Export environment-specific configurations
export const isDevelopment = config.server.isDevelopment;
export const isProduction = config.server.isProduction;
export const isTest = config.server.isTest;
