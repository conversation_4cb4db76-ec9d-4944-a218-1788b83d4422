import { Router } from 'express';
import { body, param, query, validationResult } from 'express-validator';
import { 
  getProjects, 
  getProjectById, 
  createProject, 
  updateProject 
} from '@/controllers/projectController';
import { authenticateToken, requireManager } from '@/middleware/auth';

const router = Router();

// =====================================================
// VALIDATION RULES
// =====================================================

const projectIdValidation = [
  param('id')
    .isUUID()
    .withMessage('Valid project ID is required')
];

const createProjectValidation = [
  body('name')
    .trim()
    .isLength({ min: 2, max: 255 })
    .withMessage('Project name must be between 2 and 255 characters'),
  body('description')
    .optional()
    .isLength({ max: 1000 })
    .withMessage('Description must be less than 1000 characters'),
  body('client_name')
    .optional()
    .isLength({ max: 255 })
    .withMessage('Client name must be less than 255 characters'),
  body('department_id')
    .optional()
    .isUUID()
    .withMessage('Department ID must be a valid UUID'),
  body('start_date')
    .optional()
    .isISO8601()
    .withMessage('Start date must be a valid ISO 8601 date'),
  body('end_date')
    .optional()
    .isISO8601()
    .withMessage('End date must be a valid ISO 8601 date'),
  body('priority')
    .optional()
    .isIn(['low', 'medium', 'high', 'urgent'])
    .withMessage('Priority must be low, medium, high, or urgent'),
  body('budget')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Budget must be a positive number')
];

const updateProjectValidation = [
  body('name')
    .optional()
    .trim()
    .isLength({ min: 2, max: 255 })
    .withMessage('Project name must be between 2 and 255 characters'),
  body('description')
    .optional()
    .isLength({ max: 1000 })
    .withMessage('Description must be less than 1000 characters'),
  body('client_name')
    .optional()
    .isLength({ max: 255 })
    .withMessage('Client name must be less than 255 characters'),
  body('start_date')
    .optional()
    .isISO8601()
    .withMessage('Start date must be a valid ISO 8601 date'),
  body('end_date')
    .optional()
    .isISO8601()
    .withMessage('End date must be a valid ISO 8601 date'),
  body('status')
    .optional()
    .isIn(['active', 'completed', 'on_hold', 'cancelled'])
    .withMessage('Status must be active, completed, on_hold, or cancelled'),
  body('priority')
    .optional()
    .isIn(['low', 'medium', 'high', 'urgent'])
    .withMessage('Priority must be low, medium, high, or urgent'),
  body('budget')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Budget must be a positive number')
];

const projectQueryValidation = [
  query('department_id')
    .optional()
    .isUUID()
    .withMessage('Department ID must be a valid UUID'),
  query('status')
    .optional()
    .isIn(['active', 'completed', 'on_hold', 'cancelled'])
    .withMessage('Status must be active, completed, on_hold, or cancelled'),
  query('search')
    .optional()
    .isLength({ min: 1, max: 100 })
    .withMessage('Search term must be between 1 and 100 characters')
];

// =====================================================
// VALIDATION MIDDLEWARE
// =====================================================

const handleValidationErrors = (req: any, res: any, next: any) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      message: 'Validation failed',
      errors: errors.array().map(error => ({
        field: (error as any).path || (error as any).param || 'unknown',
        message: error.msg,
        code: 'VALIDATION_ERROR'
      }))
    });
  }
  next();
};

// =====================================================
// ROUTES
// =====================================================

/**
 * @route   GET /api/projects
 * @desc    Get all projects with optional filtering
 * @access  Private
 * @query   department_id - Filter by department UUID
 * @query   status - Filter by project status
 * @query   search - Search in name, description, or client name
 * @returns Array of projects with task counts and progress
 */
router.get('/',
  authenticateToken,
  projectQueryValidation,
  handleValidationErrors,
  getProjects
);

/**
 * @route   GET /api/projects/:id
 * @desc    Get project by ID with details
 * @access  Private (department access required)
 * @param   id - Project UUID
 * @returns Project details with task counts and progress
 */
router.get('/:id',
  authenticateToken,
  projectIdValidation,
  handleValidationErrors,
  getProjectById
);

/**
 * @route   POST /api/projects
 * @desc    Create new project
 * @access  Private (Manager+ only)
 * @body    CreateProjectRequest
 * @returns Created project details
 */
router.post('/',
  authenticateToken,
  requireManager,
  createProjectValidation,
  handleValidationErrors,
  createProject
);

/**
 * @route   PUT /api/projects/:id
 * @desc    Update project
 * @access  Private (Manager+ only, department access required)
 * @param   id - Project UUID
 * @body    Partial project update data
 * @returns Updated project details
 */
router.put('/:id',
  authenticateToken,
  requireManager,
  projectIdValidation,
  updateProjectValidation,
  handleValidationErrors,
  updateProject
);

export default router;
