# Employee Status Tracker

A modern React 18 web application for tracking employee status and tasks across departments.

## 🚀 Features

### ✅ Completed Frontend Features
- **Authentication System**: Combined login/signup form with validation
- **Dashboard**: Department overview with employee statistics
- **Department View**: Detailed employee list with current tasks and status
- **Status Management**: Form for employees to update their working status
- **Floating Header**: Bottom-center floating navigation with profile and quick status controls
- **Responsive Design**: Mobile-friendly interface using Tailwind CSS
- **Real-time Status Indicators**: Visual status indicators (Active, Idle, Offline)

### 🎯 User Flow
1. **Landing Page**: User sees login/signup form
2. **Authentication**: Form validation and mock authentication
3. **Dashboard**: Overview of all departments with statistics
4. **Department Selection**: Click department to view employees
5. **Employee Status**: View current tasks and update personal status
6. **Status Updates**: Real-time reflection across the dashboard

## 🛠 Technology Stack

### Frontend
- **React 18** with TypeScript
- **Vite** for fast development and building
- **Tailwind CSS** for styling and responsive design
- **React Context** for state management
- **React Hooks** for component logic

### Project Structure
```
frontend/
├── src/
│   ├── components/
│   │   ├── AuthForm.tsx          # Login/Signup form
│   │   ├── Dashboard.tsx         # Department overview
│   │   ├── DepartmentView.tsx    # Employee list view
│   │   ├── StatusUpdateForm.tsx  # Status update modal
│   │   └── ui/
│   │       └── FloatingHeader.tsx # Bottom floating navigation bar
│   ├── contexts/
│   │   └── AuthContext.tsx       # Authentication state management
│   ├── App.tsx                   # Main application component
│   └── main.tsx                  # Application entry point
backend/                          # Placeholder for future API
database/                         # Placeholder for database schemas
```

## 🚀 Getting Started

### Prerequisites
- Node.js (v18 or higher)
- npm or yarn

### Installation
1. Navigate to the frontend directory:
   ```bash
   cd frontend
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Start the development server:
   ```bash
   npm run dev
   ```

4. Open your browser and visit: `http://localhost:5173` (or the port shown in terminal)

### 🔐 Authentication Status
**Production Ready**: Mock data has been removed and the frontend is ready for backend integration.

**API Integration Points:**
- `POST /api/auth/login` - User authentication
- `POST /api/auth/signup` - User registration
- `GET /api/departments` - Department list
- `GET /api/departments/{id}/employees` - Employee list

**Note**: Authentication will fail until backend APIs are connected.

Both users are assigned to the "Content" department. You can also create new accounts using the signup form.

### Build for Production
```bash
npm run build
```

## 📱 Features Overview

### Authentication
- **Login Form**: Email and password validation
- **Signup Form**: Name, email, password, confirm password, and department selection
- **Form Validation**: Real-time validation with error messages
- **Production Ready**: Prepared for real API integration

### Dashboard
- **Department Cards**: Visual overview of each department
- **Employee Statistics**: Active vs total employees per department
- **Progress Bars**: Visual representation of department activity
- **Quick Stats**: Overall company statistics

### Department View
- **Employee List**: All employees in the selected department
- **Status Indicators**: Visual status (Active, Idle, Offline)
- **Search Functionality**: Search by name, email, or current task
- **Current Tasks**: Display of what each employee is working on
- **Last Updated**: Timestamp of last status update

### Status Management
- **Self-Service Only**: Users can only update their own tasks and status
- **Update Form**: Modal form for updating current task and status
- **Task Description**: Text area for detailed task description
- **Status Selection**: Dropdown for Active, Idle, or Offline status
- **Form Validation**: Ensures task description is provided
- **Guidelines**: Helper text explaining status meanings
- **Security**: Backend enforces that users can only modify their own tasks

### Floating Header
- **Profile Access**: Click profile avatar to view/edit user information
- **Quick Status Updates**: One-click status changes (Active, Idle, Offline)
- **Visual Status Indicator**: Real-time status display on profile avatar
- **Responsive Design**: Adapts to different screen sizes
- **Accessibility**: Full keyboard navigation and screen reader support
- **Smooth Animations**: Floating and hover effects for better UX

## 🎨 Design Features

### Responsive Design
- **Mobile-first**: Optimized for mobile devices
- **Tablet Support**: Responsive grid layouts
- **Desktop**: Full-featured desktop experience

### UI/UX
- **Modern Design**: Clean, professional interface
- **Intuitive Navigation**: Clear user flow and navigation
- **Visual Feedback**: Loading states and hover effects
- **Accessibility**: Proper form labels and keyboard navigation

## 🔮 Future Enhancements

### Backend Integration
- REST API for authentication and data management
- Real-time WebSocket connections for live updates
- Database integration for persistent data storage

### Additional Features
- **Notifications**: Real-time notifications for status changes
- **Analytics**: Department and employee productivity analytics
- **Admin Panel**: Management interface for HR/managers
- **Time Tracking**: Integration with time tracking features
- **Reporting**: Generate reports on employee activity

## 🧪 Testing

To run tests (when implemented):
```bash
npm run test
```

## 📝 Development Notes

### Mock Data
Currently using mock data for:
- User authentication
- Department information
- Employee data
- Status updates

### State Management
- React Context for global authentication state
- Local component state for UI interactions
- Mock API calls with simulated delays

### Styling
- Tailwind CSS for utility-first styling
- Custom color palette for brand consistency
- Responsive breakpoints for all screen sizes

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.
