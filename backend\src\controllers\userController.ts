import { Request, Response } from 'express';
import { query } from '@/config/database';
import { logger } from '@/utils/logger';
import { isUserInSameDepartment } from './taskController';

// =====================================================
// USER PROFILE CONTROLLERS
// =====================================================

// Get user profile by ID
export const getUserProfile = async (req: Request, res: Response): Promise<void> => {
  try {
    const { userId } = req.params;
    const requestingUserId = req.user!.id;

    // Check access permissions
    if (requestingUserId !== userId && req.user!.role !== 'admin') {
      // For managers and employees, check if they're in the same department
      if (req.user!.role === 'manager' || req.user!.role === 'employee') {
        const sameDepart = await isUserInSameDepartment(requestingUserId, userId);
        if (!sameDepart) {
          res.status(403).json({
            success: false,
            message: 'Access denied - not in same department'
          });
          return;
        }
      } else {
        res.status(403).json({
          success: false,
          message: 'Access denied'
        });
        return;
      }
    }

    // Get user profile with department info
    const userResult = await query(`
      SELECT 
        u.id,
        u.name,
        u.email,
        u.department_id,
        d.name as department_name,
        u.role,
        u.hire_date,
        u.timezone,
        u.avatar_url,
        u.phone,
        u.emergency_contact,
        u.skills,
        u.created_at,
        u.last_login_at,
        u.is_active
      FROM users u
      LEFT JOIN departments d ON u.department_id = d.id
      WHERE u.id = $1 AND u.is_active = true
    `, [userId]);

    if (userResult.rows.length === 0) {
      res.status(404).json({
        success: false,
        message: 'User not found'
      });
      return;
    }

    const user = userResult.rows[0];

    // Get user statistics
    const statsResult = await query(`
      SELECT 
        COUNT(*) as total_tasks,
        COUNT(CASE WHEN action_type = 'completed' THEN 1 END) as completed_tasks,
        AVG(CASE WHEN action_type = 'completed' AND session_duration_minutes IS NOT NULL 
            THEN session_duration_minutes END) as avg_task_duration,
        COUNT(CASE WHEN created_at >= CURRENT_DATE - INTERVAL '30 days' THEN 1 END) as tasks_last_30_days
      FROM task_history 
      WHERE user_id = $1
    `, [userId]);

    const stats = statsResult.rows[0];

    // Get current task
    const currentTaskResult = await query(`
      SELECT 
        tu.id,
        tu.task_description,
        tu.status,
        tu.priority,
        tu.category,
        tu.progress_percentage,
        tu.expected_finish_datetime,
        tu.updated_at,
        p.name as project_name
      FROM task_updates tu
      LEFT JOIN projects p ON tu.project_id = p.id
      WHERE tu.user_id = $1
    `, [userId]);

    const currentTask = currentTaskResult.rows[0] || null;

    // Determine what information to return based on access level
    const isOwnProfile = requestingUserId === userId;
    const isAdmin = req.user!.role === 'admin';
    const fullAccess = isOwnProfile || isAdmin;

    const profileData = {
      id: user.id,
      name: user.name,
      email: user.email,
      department_id: user.department_id,
      department_name: user.department_name,
      role: user.role,
      hire_date: user.hire_date?.toISOString(),
      timezone: user.timezone,
      avatar_url: user.avatar_url,
      created_at: user.created_at.toISOString(),
      last_login_at: user.last_login_at?.toISOString(),
      is_active: user.is_active,
      // Conditional fields based on access level
      ...(fullAccess && {
        phone: user.phone,
        emergency_contact: user.emergency_contact,
      }),
      skills: user.skills || [],
      statistics: {
        total_tasks: parseInt(stats.total_tasks) || 0,
        completed_tasks: parseInt(stats.completed_tasks) || 0,
        completion_rate: stats.total_tasks > 0 
          ? Math.round((stats.completed_tasks / stats.total_tasks) * 100) 
          : 0,
        avg_task_duration: stats.avg_task_duration ? Math.round(stats.avg_task_duration) : null,
        tasks_last_30_days: parseInt(stats.tasks_last_30_days) || 0
      },
      current_task: currentTask,
      access_level: fullAccess ? 'full' : 'limited'
    };

    res.json({
      success: true,
      data: profileData
    });
  } catch (error) {
    logger.error('Get user profile error', {
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
      userId: req.params.userId,
      requestingUserId: req.user?.id
    });
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

// Get user activity/task history with enhanced details
export const getUserActivity = async (req: Request, res: Response): Promise<void> => {
  try {
    const { userId } = req.params;
    const { page = 1, limit = 20, type = 'all' } = req.query;
    const requestingUserId = req.user!.id;

    // Check access permissions (same as getUserProfile)
    if (requestingUserId !== userId && req.user!.role !== 'admin') {
      if (req.user!.role === 'manager' || req.user!.role === 'employee') {
        const sameDepart = await isUserInSameDepartment(requestingUserId, userId);
        if (!sameDepart) {
          res.status(403).json({
            success: false,
            message: 'Access denied - not in same department'
          });
          return;
        }
      } else {
        res.status(403).json({
          success: false,
          message: 'Access denied'
        });
        return;
      }
    }

    const offset = (Number(page) - 1) * Number(limit);
    
    // Build query based on type filter
    let whereClause = 'WHERE th.user_id = $1';
    const params = [userId];
    
    if (type !== 'all') {
      whereClause += ' AND th.action_type = $2';
      params.push(type as string);
    }

    const activityResult = await query(`
      SELECT 
        th.id,
        th.task_description,
        th.status,
        th.priority,
        th.category,
        th.progress_percentage,
        th.action_type,
        th.session_duration_minutes,
        th.created_at,
        p.name as project_name,
        CASE 
          WHEN th.action_type = 'completed' THEN 'success'
          WHEN th.action_type = 'cancelled' THEN 'error'
          WHEN th.action_type = 'created' THEN 'info'
          ELSE 'warning'
        END as activity_type
      FROM task_history th
      LEFT JOIN projects p ON th.project_id = p.id
      ${whereClause}
      ORDER BY th.created_at DESC
      LIMIT $${params.length + 1} OFFSET $${params.length + 2}
    `, [...params, limit, offset]);

    // Get total count for pagination
    const countResult = await query(`
      SELECT COUNT(*) as total
      FROM task_history th
      ${whereClause}
    `, params);

    const total = parseInt(countResult.rows[0].total);
    const totalPages = Math.ceil(total / Number(limit));

    res.json({
      success: true,
      data: {
        activities: activityResult.rows,
        pagination: {
          current_page: Number(page),
          total_pages: totalPages,
          total_items: total,
          items_per_page: Number(limit),
          has_next: Number(page) < totalPages,
          has_prev: Number(page) > 1
        }
      }
    });
  } catch (error) {
    logger.error('Get user activity error', {
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
      userId: req.params.userId,
      requestingUserId: req.user?.id
    });
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

// Search users (for profile discovery)
export const searchUsers = async (req: Request, res: Response): Promise<void> => {
  try {
    const { q, department, limit = 10 } = req.query;
    const requestingUserId = req.user!.id;

    if (!q || typeof q !== 'string' || q.trim().length < 2) {
      res.status(400).json({
        success: false,
        message: 'Search query must be at least 2 characters'
      });
      return;
    }

    let whereClause = `WHERE u.is_active = true AND (
      u.name ILIKE $1 OR u.email ILIKE $1
    )`;
    const params = [`%${q.trim()}%`];

    // Add department filter if specified
    if (department && typeof department === 'string') {
      whereClause += ' AND u.department_id = $2';
      params.push(department);
    }

    // For non-admin users, only show users from same department
    if (req.user!.role !== 'admin') {
      const deptParam = params.length + 1;
      whereClause += ` AND u.department_id = $${deptParam}`;
      params.push(req.user!.department_id);
    }

    const searchResult = await query(`
      SELECT 
        u.id,
        u.name,
        u.email,
        u.role,
        u.avatar_url,
        d.name as department_name,
        tu.status as current_status
      FROM users u
      LEFT JOIN departments d ON u.department_id = d.id
      LEFT JOIN task_updates tu ON u.id = tu.user_id
      ${whereClause}
      ORDER BY u.name
      LIMIT $${params.length + 1}
    `, [...params, limit]);

    res.json({
      success: true,
      data: {
        users: searchResult.rows,
        query: q,
        total_results: searchResult.rows.length
      }
    });
  } catch (error) {
    logger.error('Search users error', {
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
      query: req.query.q,
      requestingUserId: req.user?.id
    });
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};
