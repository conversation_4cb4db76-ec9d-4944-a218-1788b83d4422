# Railway Production Environment Variables
# Copy these to your Railway environment variables

NODE_ENV=production
PORT=3001
HOST=0.0.0.0

# Database - Use Railway PostgreSQL addon
DB_HOST=${{Postgres.PGHOST}}
DB_PORT=${{Postgres.PGPORT}}
DB_NAME=${{Postgres.PGDATABASE}}
DB_USER=${{Postgres.PGUSER}}
DB_PASSWORD=${{Postgres.PGPASSWORD}}
DB_SSL=true

# JWT - Generate strong secrets for production
JWT_SECRET=your-super-secret-jwt-key-at-least-32-chars-change-this
JWT_EXPIRES_IN=24h
JWT_REFRESH_SECRET=your-refresh-secret-key-change-this
JWT_REFRESH_EXPIRES_IN=7d

# Redis - Use Railway Redis addon if available
REDIS_HOST=${{Redis.REDIS_HOST}}
REDIS_PORT=${{Redis.REDIS_PORT}}
REDIS_PASSWORD=${{Redis.REDIS_PASSWORD}}
REDIS_DB=0

# CORS - Set to your frontend URL
CORS_ORIGIN=https://employee-task-2-production.up.railway.app
CORS_CREDENTIALS=true

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# File Upload
MAX_FILE_SIZE=5242880
UPLOAD_PATH=uploads

# Logging
LOG_LEVEL=info
LOG_FILE=logs/app.log
ENABLE_MORGAN_LOGGING=false

# WebSocket - Set to your frontend URL
WS_CORS_ORIGIN=https://employee-task-2-production.up.railway.app

# Analytics
ANALYTICS_REFRESH_INTERVAL=3600000
ENABLE_REAL_TIME_ANALYTICS=true

# Security
BCRYPT_ROUNDS=12
SESSION_SECRET=your-session-secret-change-this

# Production settings
ENABLE_SWAGGER=false
ENABLE_DETAILED_ERRORS=false
