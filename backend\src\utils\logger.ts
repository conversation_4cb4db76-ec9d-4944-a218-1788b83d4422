import winston from 'winston';
import path from 'path';
import { config } from '@/config';

// Custom log format
const logFormat = winston.format.combine(
  winston.format.timestamp({
    format: 'YYYY-MM-DD HH:mm:ss'
  }),
  winston.format.errors({ stack: true }),
  winston.format.json(),
  winston.format.prettyPrint()
);

// Console format for development
const consoleFormat = winston.format.combine(
  winston.format.colorize(),
  winston.format.timestamp({
    format: 'HH:mm:ss'
  }),
  winston.format.printf(({ timestamp, level, message, ...meta }) => {
    let log = `${timestamp} [${level}]: ${message}`;
    
    if (Object.keys(meta).length > 0) {
      log += `\n${JSON.stringify(meta, null, 2)}`;
    }
    
    return log;
  })
);

// Create transports
const transports: winston.transport[] = [
  // Console transport
  new winston.transports.Console({
    format: config.server.isDevelopment ? consoleFormat : logFormat,
    level: config.logging.level
  })
];

// Add file transport in production
if (config.server.isProduction) {
  // Ensure logs directory exists
  const fs = require('fs');
  const logsDir = path.dirname(config.logging.file);
  if (!fs.existsSync(logsDir)) {
    fs.mkdirSync(logsDir, { recursive: true });
  }

  transports.push(
    // Error log file
    new winston.transports.File({
      filename: path.join(logsDir, 'error.log'),
      level: 'error',
      format: logFormat,
      maxsize: 5242880, // 5MB
      maxFiles: 5
    }),
    
    // Combined log file
    new winston.transports.File({
      filename: config.logging.file,
      format: logFormat,
      maxsize: 5242880, // 5MB
      maxFiles: 5
    })
  );
}

// Create logger instance
export const logger = winston.createLogger({
  level: config.logging.level,
  format: logFormat,
  transports,
  exitOnError: false
});

// Add request logging helper
export const logRequest = (req: any, res: any, responseTime?: number) => {
  const logData = {
    method: req.method,
    url: req.url,
    ip: req.ip || req.connection.remoteAddress,
    userAgent: req.get('User-Agent'),
    statusCode: res.statusCode,
    responseTime: responseTime ? `${responseTime}ms` : undefined,
    userId: req.user?.id,
    requestId: req.id
  };

  if (res.statusCode >= 400) {
    logger.warn('HTTP Request', logData);
  } else {
    logger.info('HTTP Request', logData);
  }
};

// Add error logging helper
export const logError = (error: Error, context?: any) => {
  logger.error('Application Error', {
    message: error.message,
    stack: error.stack,
    context
  });
};

// Add database query logging helper
export const logQuery = (query: string, params?: any[], duration?: number) => {
  if (config.server.isDevelopment) {
    logger.debug('Database Query', {
      query: query.replace(/\s+/g, ' ').trim(),
      params,
      duration: duration ? `${duration}ms` : undefined
    });
  }
};

// Add authentication logging helper
export const logAuth = (action: string, userId?: string, details?: any) => {
  logger.info('Authentication Event', {
    action,
    userId,
    ...details
  });
};

// Add analytics logging helper
export const logAnalytics = (event: string, data?: any) => {
  logger.info('Analytics Event', {
    event,
    data,
    timestamp: new Date().toISOString()
  });
};

// Stream for Morgan HTTP logging
export const morganStream = {
  write: (message: string) => {
    logger.info(message.trim());
  }
};

// Performance monitoring helper
export const performanceLogger = {
  start: (operation: string) => {
    const start = Date.now();
    return {
      end: (details?: any) => {
        const duration = Date.now() - start;
        logger.debug('Performance Metric', {
          operation,
          duration: `${duration}ms`,
          ...details
        });
        return duration;
      }
    };
  }
};

// Health check logging
export const logHealthCheck = (service: string, status: 'healthy' | 'unhealthy', details?: any) => {
  const level = status === 'healthy' ? 'info' : 'error';
  logger.log(level, 'Health Check', {
    service,
    status,
    details
  });
};

// Startup logging
export const logStartup = (message: string, details?: any) => {
  logger.info(`🚀 ${message}`, details);
};

// Shutdown logging
export const logShutdown = (message: string, details?: any) => {
  logger.info(`🛑 ${message}`, details);
};
