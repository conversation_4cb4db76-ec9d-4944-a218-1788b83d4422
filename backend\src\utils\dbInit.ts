import { pool } from '@/config/database';
import { logger } from '@/utils/logger';

/**
 * Database initialization script
 * Creates all necessary tables and adds basic departments including Content department
 * This script is designed to be run automatically during deployment
 */

const createTablesSQL = `
-- Enable UUID extension for better performance and security
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Enable pg_stat_statements for query performance monitoring
CREATE EXTENSION IF NOT EXISTS pg_stat_statements;

-- =====================================================
-- CORE TABLES
-- =====================================================

-- Departments table
CREATE TABLE IF NOT EXISTS departments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    manager_id UUID, -- Self-referencing to users table
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT true
);

-- Users table (employees)
CREATE TABLE IF NOT EXISTS users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email VARCHAR(255) NOT NULL UNIQUE,
    name VARCHAR(255) NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    department_id UUID NOT NULL REFERENCES departments(id),
    role VARCHAR(50) DEFAULT 'employee', -- employee, manager, admin
    hire_date DATE,
    timezone VARCHAR(50) DEFAULT 'UTC',
    avatar_url TEXT,
    phone VARCHAR(20),
    emergency_contact JSONB, -- Flexible structure for emergency contact info
    skills JSONB, -- Array of skills for analytics
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    last_login_at TIMESTAMP WITH TIME ZONE,
    is_active BOOLEAN DEFAULT true
);

-- Projects table for better organization
CREATE TABLE IF NOT EXISTS projects (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    client_name VARCHAR(255),
    department_id UUID REFERENCES departments(id),
    start_date DATE,
    end_date DATE,
    status VARCHAR(50) DEFAULT 'active', -- active, completed, on_hold, cancelled
    priority VARCHAR(20) DEFAULT 'medium', -- low, medium, high, urgent
    budget DECIMAL(12,2),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT true
);

-- Task updates table (current status)
CREATE TABLE IF NOT EXISTS task_updates (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id),
    task_description TEXT NOT NULL,
    status VARCHAR(20) NOT NULL CHECK (status IN ('active', 'idle', 'offline')),
    priority VARCHAR(20) NOT NULL CHECK (priority IN ('low', 'medium', 'high', 'urgent')),
    category VARCHAR(50) NOT NULL CHECK (category IN ('question-creation', 'project-delivery', 'uploading', 'quality-checking')),
    estimated_duration_minutes INTEGER, -- Stored in minutes for easy calculations
    progress_percentage INTEGER DEFAULT 0 CHECK (progress_percentage >= 0 AND progress_percentage <= 100),
    project_id UUID REFERENCES projects(id),
    expected_completion_date DATE,
    blocking_issues TEXT,
    number_of_questions INTEGER,
    expected_finish_datetime TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    -- Ensure one active task per user
    CONSTRAINT unique_active_task_per_user UNIQUE (user_id) DEFERRABLE INITIALLY DEFERRED
);

-- Task history table (audit trail)
CREATE TABLE IF NOT EXISTS task_history (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id),
    task_update_id UUID REFERENCES task_updates(id),
    task_description TEXT NOT NULL,
    status VARCHAR(20) NOT NULL,
    priority VARCHAR(20) NOT NULL,
    category VARCHAR(50) NOT NULL,
    estimated_duration_minutes INTEGER,
    progress_percentage INTEGER,
    project_id UUID REFERENCES projects(id),
    expected_completion_date DATE,
    blocking_issues TEXT,
    number_of_questions INTEGER,
    expected_finish_datetime TIMESTAMP WITH TIME ZONE,
    action_type VARCHAR(20) NOT NULL CHECK (action_type IN ('created', 'updated', 'completed', 'cancelled')),
    session_duration_minutes INTEGER, -- How long they worked on this task
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Task tags for flexible categorization
CREATE TABLE IF NOT EXISTS task_tags (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL UNIQUE,
    color VARCHAR(7) DEFAULT '#6B7280', -- Hex color code
    description TEXT,
    usage_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT true
);

-- Many-to-many relationship between tasks and tags
CREATE TABLE IF NOT EXISTS task_update_tags (
    task_update_id UUID REFERENCES task_updates(id) ON DELETE CASCADE,
    task_tag_id UUID REFERENCES task_tags(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (task_update_id, task_tag_id)
);

-- User sessions for analytics
CREATE TABLE IF NOT EXISTS user_sessions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id),
    session_start TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    session_end TIMESTAMP WITH TIME ZONE,
    ip_address INET,
    user_agent TEXT,
    total_tasks_completed INTEGER DEFAULT 0,
    total_active_time_minutes INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- ANALYTICS TABLES
-- =====================================================

-- Daily productivity summary (pre-aggregated for performance)
CREATE TABLE IF NOT EXISTS daily_productivity_summary (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id),
    date DATE NOT NULL,
    total_tasks_completed INTEGER DEFAULT 0,
    total_active_time_minutes INTEGER DEFAULT 0,
    total_idle_time_minutes INTEGER DEFAULT 0,
    average_task_completion_time_minutes DECIMAL(10,2),
    productivity_score DECIMAL(5,2), -- 0-100 score
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE(user_id, date)
);

-- Department-level metrics
CREATE TABLE IF NOT EXISTS department_metrics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    department_id UUID NOT NULL REFERENCES departments(id),
    date DATE NOT NULL,
    total_employees INTEGER DEFAULT 0,
    active_employees INTEGER DEFAULT 0,
    total_tasks_completed INTEGER DEFAULT 0,
    average_productivity_score DECIMAL(5,2),
    total_project_count INTEGER DEFAULT 0,
    completed_project_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE(department_id, date)
);

-- Task performance metrics
CREATE TABLE IF NOT EXISTS task_performance_metrics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    category VARCHAR(50) NOT NULL,
    priority VARCHAR(20) NOT NULL,
    date DATE NOT NULL,
    total_tasks INTEGER DEFAULT 0,
    completed_tasks INTEGER DEFAULT 0,
    average_completion_time_minutes DECIMAL(10,2),
    average_estimated_vs_actual_ratio DECIMAL(5,2),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE(category, priority, date)
);
`;

const createIndexesSQL = `
-- =====================================================
-- PERFORMANCE INDEXES
-- =====================================================

-- Users table indexes
CREATE INDEX IF NOT EXISTS idx_users_department ON users(department_id);
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_active ON users(is_active);
CREATE INDEX IF NOT EXISTS idx_users_role ON users(role);

-- Task updates indexes
CREATE INDEX IF NOT EXISTS idx_task_updates_user ON task_updates(user_id);
CREATE INDEX IF NOT EXISTS idx_task_updates_status ON task_updates(status);
CREATE INDEX IF NOT EXISTS idx_task_updates_project ON task_updates(project_id);
CREATE INDEX IF NOT EXISTS idx_task_updates_created ON task_updates(created_at);
CREATE INDEX IF NOT EXISTS idx_task_updates_category ON task_updates(category);
CREATE INDEX IF NOT EXISTS idx_task_updates_priority ON task_updates(priority);

-- Task history indexes
CREATE INDEX IF NOT EXISTS idx_task_history_user_created ON task_history(user_id, created_at);
CREATE INDEX IF NOT EXISTS idx_task_history_project ON task_history(project_id);
CREATE INDEX IF NOT EXISTS idx_task_history_action_type ON task_history(action_type);
CREATE INDEX IF NOT EXISTS idx_task_history_created ON task_history(created_at);

-- Projects indexes
CREATE INDEX IF NOT EXISTS idx_projects_department ON projects(department_id);
CREATE INDEX IF NOT EXISTS idx_projects_status ON projects(status);
CREATE INDEX IF NOT EXISTS idx_projects_dates ON projects(start_date, end_date);

-- Analytics indexes
CREATE INDEX IF NOT EXISTS idx_daily_productivity_user_date ON daily_productivity_summary(user_id, date);
CREATE INDEX IF NOT EXISTS idx_department_metrics_dept_date ON department_metrics(department_id, date);
CREATE INDEX IF NOT EXISTS idx_task_performance_category_date ON task_performance_metrics(category, date);

-- User sessions indexes
CREATE INDEX IF NOT EXISTS idx_user_sessions_user_start ON user_sessions(user_id, session_start);
CREATE INDEX IF NOT EXISTS idx_user_sessions_start ON user_sessions(session_start);
`;

const createTriggersSQL = `
-- =====================================================
-- TRIGGERS FOR AUTOMATIC UPDATES
-- =====================================================

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply updated_at triggers to relevant tables
CREATE TRIGGER update_departments_updated_at BEFORE UPDATE ON departments
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_projects_updated_at BEFORE UPDATE ON projects
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_task_updates_updated_at BEFORE UPDATE ON task_updates
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_daily_productivity_updated_at BEFORE UPDATE ON daily_productivity_summary
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_department_metrics_updated_at BEFORE UPDATE ON department_metrics
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
`;

const insertContentDepartmentSQL = `
-- =====================================================
-- CONTENT DEPARTMENT ONLY
-- =====================================================

-- Insert only Content department as requested
INSERT INTO departments (id, name, description) VALUES
('550e8400-e29b-41d4-a716-446655440007', 'Content', 'Content creation, writing, and editorial services')
ON CONFLICT (name) DO NOTHING;
`;

/**
 * Validate required environment variables for database connection
 * Supports Railway DATABASE_URL, PG* variables, and standard DB_* environment variables
 */
const validateDatabaseEnvironment = (): void => {
  const databaseUrl = process.env['DATABASE_URL'];
  if (!databaseUrl) {
    console.error('❌ DATABASE_URL environment variable is missing');
    console.error('Available database-related environment variables:');
    Object.keys(process.env)
      .filter(key => key.includes('DB_') || key.includes('PG') || key.includes('DATABASE'))
      .forEach(key => console.error(`  ${key}: ${key.includes('PASSWORD') ? '[HIDDEN]' : process.env[key]}`));

    throw new Error('DATABASE_URL is required. Please ensure Railway PostgreSQL addon is connected.');
  }

  try {
    const url = new URL(databaseUrl);
    if (!url.hostname || !url.pathname || !url.username) {
      throw new Error('Invalid DATABASE_URL format');
    }
    console.log('✅ DATABASE_URL is valid and complete');
  } catch (error) {
    console.error('❌ DATABASE_URL is invalid:', error);
    throw new Error('DATABASE_URL is invalid. Please check Railway PostgreSQL addon configuration.');
  }
};

/**
 * Initialize database tables and basic data
 */
export const initializeDatabase = async (): Promise<void> => {
  let client;

  try {
    console.log('🚀 Starting database initialization...');
    logger.info('Starting database initialization...');

    // Validate environment variables
    console.log('🔧 Validating database environment variables...');
    validateDatabaseEnvironment();
    console.log('✅ Database environment variables validated');

    // Log database configuration
    console.log('🔍 Using DATABASE_URL for connection');
    console.log('🔌 Database connection configured');
    logger.info('Database connection configured using DATABASE_URL');

    client = await pool.connect();
    logger.info('Database connection established for initialization');

    // Start transaction
    await client.query('BEGIN');
    logger.info('Transaction started');

    // Create tables with detailed error handling
    console.log('🔧 Creating database tables...');
    logger.info('Creating database tables...');
    try {
      await client.query(createTablesSQL);
      console.log('✅ Database tables created successfully');
      logger.info('Database tables created successfully');
    } catch (tableError) {
      console.error('❌ Failed to create tables:', tableError);
      logger.error('Failed to create tables:', tableError);
      throw new Error(`Table creation failed: ${tableError instanceof Error ? tableError.message : String(tableError)}`);
    }

    // Create indexes with detailed error handling
    console.log('🔧 Creating database indexes...');
    logger.info('Creating database indexes...');
    try {
      await client.query(createIndexesSQL);
      console.log('✅ Database indexes created successfully');
      logger.info('Database indexes created successfully');
    } catch (indexError) {
      console.error('❌ Failed to create indexes:', indexError);
      logger.error('Failed to create indexes:', indexError);
      throw new Error(`Index creation failed: ${indexError instanceof Error ? indexError.message : String(indexError)}`);
    }

    // Create triggers with detailed error handling
    console.log('🔧 Creating database triggers...');
    logger.info('Creating database triggers...');
    try {
      await client.query(createTriggersSQL);
      console.log('✅ Database triggers created successfully');
      logger.info('Database triggers created successfully');
    } catch (triggerError) {
      console.error('❌ Failed to create triggers:', triggerError);
      logger.error('Failed to create triggers:', triggerError);
      throw new Error(`Trigger creation failed: ${triggerError instanceof Error ? triggerError.message : String(triggerError)}`);
    }

    // Insert Content department only
    console.log('🔧 Inserting Content department...');
    logger.info('Inserting Content department...');
    try {
      await client.query(insertContentDepartmentSQL);
      console.log('✅ Content department inserted successfully');
      logger.info('Content department inserted successfully');
    } catch (deptError) {
      console.error('❌ Failed to insert Content department:', deptError);
      logger.error('Failed to insert Content department:', deptError);
      throw new Error(`Department insertion failed: ${deptError instanceof Error ? deptError.message : String(deptError)}`);
    }

    // Verify tables were created
    console.log('🔍 Verifying table creation...');
    try {
      const verifyResult = await client.query(`
        SELECT table_name
        FROM information_schema.tables
        WHERE table_schema = 'public'
        AND table_name IN ('departments', 'users', 'task_updates', 'projects')
        ORDER BY table_name
      `);
      const createdTables = verifyResult.rows.map(row => row.table_name);
      console.log('✅ Created tables:', createdTables);
      logger.info('Created tables:', createdTables);

      if (createdTables.length < 4) {
        throw new Error(`Expected 4 core tables, but only found ${createdTables.length}: ${createdTables.join(', ')}`);
      }
    } catch (verifyError) {
      console.error('❌ Table verification failed:', verifyError);
      logger.error('Table verification failed:', verifyError);
      throw verifyError;
    }

    // Commit transaction
    console.log('🔧 Committing transaction...');
    await client.query('COMMIT');
    console.log('✅ Transaction committed successfully');
    logger.info('Transaction committed successfully');

    logger.info('✅ Database initialization completed successfully');

  } catch (error) {
    logger.error('❌ Database initialization failed:', error);

    if (client) {
      try {
        await client.query('ROLLBACK');
        logger.info('Transaction rolled back');
      } catch (rollbackError) {
        logger.error('Failed to rollback transaction:', rollbackError);
      }
    }

    throw error;
  } finally {
    if (client) {
      client.release();
      logger.info('Database client released');
    }
  }
};

/**
 * Check if database is already initialized
 */
export const isDatabaseInitialized = async (): Promise<boolean> => {
  const client = await pool.connect();
  
  try {
    const result = await client.query(`
      SELECT COUNT(*) as table_count 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      AND table_name IN ('departments', 'users', 'task_updates', 'projects')
    `);
    
    return parseInt(result.rows[0].table_count) >= 4;
  } catch (error) {
    logger.error('Error checking database initialization status:', error);
    return false;
  } finally {
    client.release();
  }
};
