-- Analytics Queries for Employee Task Dashboard
-- These queries demonstrate the analytics capabilities of the database schema

-- =====================================================
-- REAL-TIME DASHBOARD QUERIES
-- =====================================================

-- Current department overview (for main dashboard)
SELECT 
    d.name as department_name,
    COUNT(u.id) as total_employees,
    COUNT(CASE WHEN tu.status = 'active' THEN 1 END) as active_employees,
    COUNT(CASE WHEN tu.status = 'idle' THEN 1 END) as idle_employees,
    COUNT(CASE WHEN tu.status = 'offline' THEN 1 END) as offline_employees,
    ROUND(
        COUNT(CASE WHEN tu.status = 'active' THEN 1 END) * 100.0 / 
        NULLIF(COUNT(u.id), 0), 2
    ) as activity_percentage,
    COUNT(CASE WHEN tu.priority = 'urgent' THEN 1 END) as urgent_tasks,
    COUNT(CASE WHEN tu.blocking_issues IS NOT NULL AND tu.blocking_issues != '' THEN 1 END) as blocked_tasks
FROM departments d
LEFT JOIN users u ON d.id = u.department_id AND u.is_active = true
LEFT JOIN task_updates tu ON u.id = tu.user_id
WHERE d.is_active = true
GROUP BY d.id, d.name
ORDER BY d.name;

-- Employee status with last update time (for department view)
SELECT 
    u.id,
    u.name,
    u.email,
    d.name as department,
    tu.task_description,
    tu.status,
    tu.priority,
    tu.category,
    tu.progress_percentage,
    p.name as project_name,
    tu.expected_completion_date,
    tu.blocking_issues,
    tu.updated_at,
    EXTRACT(EPOCH FROM (CURRENT_TIMESTAMP - tu.updated_at)) / 60 as minutes_since_update,
    CASE 
        WHEN EXTRACT(EPOCH FROM (CURRENT_TIMESTAMP - tu.updated_at)) / 60 < 30 THEN 'recent'
        WHEN EXTRACT(EPOCH FROM (CURRENT_TIMESTAMP - tu.updated_at)) / 60 < 120 THEN 'moderate'
        ELSE 'stale'
    END as update_freshness
FROM users u
JOIN departments d ON u.department_id = d.id
LEFT JOIN task_updates tu ON u.id = tu.user_id
LEFT JOIN projects p ON tu.project_id = p.id
WHERE u.is_active = true
ORDER BY d.name, u.name;

-- =====================================================
-- PRODUCTIVITY ANALYTICS
-- =====================================================

-- Daily productivity by user (last 30 days)
SELECT 
    u.name,
    d.name as department,
    DATE(th.created_at) as date,
    COUNT(*) as status_updates,
    COUNT(CASE WHEN th.action_type = 'completed' THEN 1 END) as tasks_completed,
    AVG(th.session_duration_minutes) as avg_session_duration,
    SUM(CASE WHEN th.status = 'active' THEN th.session_duration_minutes ELSE 0 END) as active_minutes,
    ROUND(
        SUM(CASE WHEN th.status = 'active' THEN th.session_duration_minutes ELSE 0 END) / 
        NULLIF(SUM(th.session_duration_minutes), 0) * 100, 2
    ) as productivity_percentage
FROM task_history th
JOIN users u ON th.user_id = u.id
JOIN departments d ON u.department_id = d.id
WHERE th.created_at >= CURRENT_DATE - INTERVAL '30 days'
    AND th.session_duration_minutes IS NOT NULL
GROUP BY u.id, u.name, d.name, DATE(th.created_at)
ORDER BY date DESC, u.name;

-- Task completion trends by category
SELECT 
    category,
    DATE_TRUNC('week', created_at) as week,
    COUNT(*) as total_tasks,
    COUNT(CASE WHEN action_type = 'completed' THEN 1 END) as completed_tasks,
    ROUND(
        COUNT(CASE WHEN action_type = 'completed' THEN 1 END) * 100.0 / 
        NULLIF(COUNT(*), 0), 2
    ) as completion_rate,
    AVG(session_duration_minutes) as avg_duration
FROM task_history
WHERE created_at >= CURRENT_DATE - INTERVAL '12 weeks'
GROUP BY category, DATE_TRUNC('week', created_at)
ORDER BY week DESC, category;

-- =====================================================
-- WORKLOAD ANALYSIS
-- =====================================================

-- Current workload distribution by priority
SELECT 
    d.name as department,
    tu.priority,
    COUNT(*) as task_count,
    ROUND(COUNT(*) * 100.0 / SUM(COUNT(*)) OVER (PARTITION BY d.name), 2) as percentage,
    AVG(tu.progress_percentage) as avg_progress,
    COUNT(CASE WHEN tu.blocking_issues IS NOT NULL AND tu.blocking_issues != '' THEN 1 END) as blocked_count
FROM task_updates tu
JOIN users u ON tu.user_id = u.id
JOIN departments d ON u.department_id = d.id
WHERE u.is_active = true
GROUP BY d.name, tu.priority
ORDER BY d.name, 
    CASE tu.priority 
        WHEN 'urgent' THEN 1 
        WHEN 'high' THEN 2 
        WHEN 'medium' THEN 3 
        WHEN 'low' THEN 4 
    END;

-- Overdue tasks analysis
SELECT 
    u.name,
    d.name as department,
    tu.task_description,
    tu.priority,
    tu.expected_completion_date,
    CURRENT_DATE - tu.expected_completion_date as days_overdue,
    tu.progress_percentage,
    tu.blocking_issues
FROM task_updates tu
JOIN users u ON tu.user_id = u.id
JOIN departments d ON u.department_id = d.id
WHERE tu.expected_completion_date < CURRENT_DATE
    AND tu.status != 'offline'
    AND u.is_active = true
ORDER BY days_overdue DESC, tu.priority;

-- =====================================================
-- PROJECT ANALYTICS
-- =====================================================

-- Project progress overview
SELECT 
    p.name as project_name,
    p.client_name,
    d.name as department,
    p.status as project_status,
    p.priority,
    COUNT(tu.id) as active_tasks,
    AVG(tu.progress_percentage) as avg_task_progress,
    COUNT(CASE WHEN tu.priority = 'urgent' THEN 1 END) as urgent_tasks,
    COUNT(CASE WHEN tu.blocking_issues IS NOT NULL AND tu.blocking_issues != '' THEN 1 END) as blocked_tasks,
    p.start_date,
    p.end_date,
    CASE 
        WHEN p.end_date < CURRENT_DATE THEN 'overdue'
        WHEN p.end_date - CURRENT_DATE <= 7 THEN 'due_soon'
        ELSE 'on_track'
    END as timeline_status
FROM projects p
LEFT JOIN departments d ON p.department_id = d.id
LEFT JOIN task_updates tu ON p.id = tu.project_id
WHERE p.is_active = true
GROUP BY p.id, p.name, p.client_name, d.name, p.status, p.priority, p.start_date, p.end_date
ORDER BY p.priority, p.end_date;

-- =====================================================
-- TEAM PERFORMANCE METRICS
-- =====================================================

-- Department efficiency comparison (last 30 days)
SELECT 
    d.name as department,
    COUNT(DISTINCT u.id) as team_size,
    COUNT(th.id) as total_activities,
    COUNT(CASE WHEN th.action_type = 'completed' THEN 1 END) as completed_tasks,
    ROUND(
        COUNT(CASE WHEN th.action_type = 'completed' THEN 1 END) * 100.0 / 
        NULLIF(COUNT(th.id), 0), 2
    ) as completion_rate,
    AVG(th.session_duration_minutes) as avg_session_duration,
    SUM(CASE WHEN th.status = 'active' THEN th.session_duration_minutes ELSE 0 END) as total_active_minutes,
    ROUND(
        SUM(CASE WHEN th.status = 'active' THEN th.session_duration_minutes ELSE 0 END) / 
        NULLIF(COUNT(DISTINCT u.id), 0), 2
    ) as avg_active_minutes_per_employee
FROM departments d
LEFT JOIN users u ON d.id = u.department_id AND u.is_active = true
LEFT JOIN task_history th ON u.id = th.user_id 
    AND th.created_at >= CURRENT_DATE - INTERVAL '30 days'
WHERE d.is_active = true
GROUP BY d.id, d.name
ORDER BY completion_rate DESC;

-- Top performers by task completion
SELECT 
    u.name,
    d.name as department,
    COUNT(CASE WHEN th.action_type = 'completed' THEN 1 END) as tasks_completed,
    COUNT(th.id) as total_activities,
    ROUND(
        COUNT(CASE WHEN th.action_type = 'completed' THEN 1 END) * 100.0 / 
        NULLIF(COUNT(th.id), 0), 2
    ) as completion_rate,
    AVG(th.session_duration_minutes) as avg_session_duration,
    COUNT(CASE WHEN th.priority = 'urgent' AND th.action_type = 'completed' THEN 1 END) as urgent_completed
FROM users u
JOIN departments d ON u.department_id = d.id
LEFT JOIN task_history th ON u.id = th.user_id 
    AND th.created_at >= CURRENT_DATE - INTERVAL '30 days'
WHERE u.is_active = true
GROUP BY u.id, u.name, d.name
HAVING COUNT(th.id) > 0
ORDER BY completion_rate DESC, tasks_completed DESC
LIMIT 10;

-- =====================================================
-- BLOCKING ISSUES ANALYSIS
-- =====================================================

-- Most common blocking issues
SELECT 
    LOWER(TRIM(unnest(string_to_array(blocking_issues, ',')))) as blocking_issue,
    COUNT(*) as frequency,
    AVG(progress_percentage) as avg_progress_when_blocked,
    COUNT(DISTINCT user_id) as affected_users,
    COUNT(DISTINCT project_id) as affected_projects
FROM task_updates
WHERE blocking_issues IS NOT NULL 
    AND blocking_issues != ''
    AND LENGTH(blocking_issues) > 5
GROUP BY LOWER(TRIM(unnest(string_to_array(blocking_issues, ','))))
HAVING COUNT(*) > 1
ORDER BY frequency DESC
LIMIT 20;
