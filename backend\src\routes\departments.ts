import { Router } from 'express';
import { param, query, validationResult } from 'express-validator';
import { 
  getDepartments, 
  getDepartmentById, 
  getDepartmentEmployees 
} from '@/controllers/departmentController';
import { authenticateToken, requireDepartmentAccess } from '@/middleware/auth';

const router = Router();

// =====================================================
// VALIDATION RULES
// =====================================================

const departmentIdValidation = [
  param('id')
    .isUUID()
    .withMessage('Valid department ID is required')
];

const employeeQueryValidation = [
  query('search')
    .optional()
    .isLength({ min: 1, max: 100 })
    .withMessage('Search term must be between 1 and 100 characters'),
  query('status')
    .optional()
    .isIn(['active', 'idle', 'offline'])
    .withMessage('Status must be active, idle, or offline'),
  query('priority')
    .optional()
    .isIn(['low', 'medium', 'high', 'urgent'])
    .withMessage('Priority must be low, medium, high, or urgent')
];

// =====================================================
// VALIDATION MIDDLEWARE
// =====================================================

const handleValidationErrors = (req: any, res: any, next: any) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      message: 'Validation failed',
      errors: errors.array().map(error => ({
        field: (error as any).path || (error as any).param || 'unknown',
        message: error.msg,
        code: 'VALIDATION_ERROR'
      }))
    });
  }
  next();
};

// =====================================================
// ROUTES
// =====================================================

/**
 * @route   GET /api/departments
 * @desc    Get all departments with summary data for dashboard
 * @access  Private
 * @returns Array of departments with employee counts and activity stats
 */
router.get('/',
  authenticateToken,
  getDepartments
);

/**
 * @route   GET /api/departments/:id
 * @desc    Get department details with employees and projects
 * @access  Private (department access required)
 * @param   id - Department UUID
 * @returns Department details with employees, projects, and metrics
 */
router.get('/:id',
  authenticateToken,
  departmentIdValidation,
  handleValidationErrors,
  requireDepartmentAccess,
  getDepartmentById
);

/**
 * @route   GET /api/departments/:id/employees
 * @desc    Get employees in department with optional filtering
 * @access  Private (department access required)
 * @param   id - Department UUID
 * @query   search - Search term for name, email, or task
 * @query   status - Filter by employee status (active, idle, offline)
 * @query   priority - Filter by task priority (low, medium, high, urgent)
 * @returns Array of employees with current task information
 */
router.get('/:id/employees',
  authenticateToken,
  departmentIdValidation,
  employeeQueryValidation,
  handleValidationErrors,
  requireDepartmentAccess,
  getDepartmentEmployees
);

export default router;
