-- Migration: Add new task fields for questions and finish time
-- Date: 2025-01-30
-- Description: Add number_of_questions and expected_finish_datetime fields

BEGIN;

-- Add new columns to task_updates table
ALTER TABLE task_updates 
ADD COLUMN number_of_questions INTEGER,
ADD COLUMN expected_finish_datetime TIMESTAMP WITH TIME ZONE;

-- Add new columns to task_history table
ALTER TABLE task_history 
ADD COLUMN number_of_questions INTEGER,
ADD COLUMN expected_finish_datetime TIMESTAMP WITH TIME ZONE;

-- Update the trigger function to include new fields
CREATE OR REPLACE FUNCTION update_task_history()
RETURNS TRIGGER AS $$
    DECLARE
        session_duration INTEGER;
    BEGIN
        -- Calculate session duration if this is an update
        IF TG_OP = 'UPDATE' THEN
            session_duration := EXTRACT(EPOCH FROM (CURRENT_TIMESTAMP - OLD.updated_at)) / 60;
        ELSE
            session_duration := 0;
        END IF;

        -- Insert into task history
        INSERT INTO task_history (
            user_id,
            task_update_id,
            task_description,
            status,
            priority,
            category,
            estimated_duration_minutes,
            progress_percentage,
            project_id,
            expected_completion_date,
            blocking_issues,
            number_of_questions,
            expected_finish_datetime,
            action_type,
            session_duration_minutes
        ) VALUES (
            NEW.user_id,
            NEW.id,
            NEW.task_description,
            NEW.status,
            NEW.priority,
            NEW.category,
            NEW.estimated_duration_minutes,
            NEW.progress_percentage,
            NEW.project_id,
            NEW.expected_completion_date,
            NEW.blocking_issues,
            NEW.number_of_questions,
            NEW.expected_finish_datetime,
            CASE
                WHEN TG_OP = 'INSERT' THEN 'created'
                WHEN TG_OP = 'UPDATE' THEN 'updated'
                ELSE 'unknown'
            END,
            session_duration
        );

        RETURN NEW;
    END;
$$ language 'plpgsql';

COMMIT;

-- Verify the migration
\d task_updates;
\d task_history;
