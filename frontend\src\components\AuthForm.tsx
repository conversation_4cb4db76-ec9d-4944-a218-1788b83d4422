import React, { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { <PERSON><PERSON>, <PERSON>, CardContent, CardHeader, CardTitle, Input } from './ui';
import { createApiUrl } from '../config/api';

interface FormData {
  firstName: string;
  lastName: string;
  email: string;
  password: string;
  confirmPassword: string;
  departmentId: string;
}

interface FormErrors {
  firstName?: string;
  lastName?: string;
  email?: string;
  password?: string;
  confirmPassword?: string;
  departmentId?: string;
  general?: string;
}

interface Department {
  id: string;
  name: string;
  description?: string;
}

const AuthForm: React.FC = () => {
  const [isLogin, setIsLogin] = useState(true);
  const [formData, setFormData] = useState<FormData>({
    firstName: '',
    lastName: '',
    email: '',
    password: '',
    confirmPassword: '',
    departmentId: ''
  });
  const [errors, setErrors] = useState<FormErrors>({});
  const [departments, setDepartments] = useState<Department[]>([]);
  const [isDepartmentsLoading, setIsDepartmentsLoading] = useState(false);
  const { login, signup, isLoading } = useAuth();

  // Fetch departments for signup form
  useEffect(() => {
    const fetchDepartments = async () => {
      if (isLogin) return; // Only fetch when showing signup form

      setIsDepartmentsLoading(true);
      try {
        const apiUrl = createApiUrl('api/auth/departments');
        console.log('Fetching departments from:', apiUrl);
        const response = await fetch(apiUrl);
        if (response.ok) {
          const data = await response.json();
          if (data.success && data.data) {
            setDepartments(data.data);
            // Set first department as default if available
            if (data.data.length > 0 && !formData.departmentId) {
              setFormData(prev => ({ ...prev, departmentId: data.data[0].id }));
            }
          }
        } else {
          console.error('Failed to fetch departments:', response.statusText);
        }
      } catch (error) {
        console.error('Error fetching departments:', error);
      } finally {
        setIsDepartmentsLoading(false);
      }
    };

    fetchDepartments();
  }, [isLogin, formData.departmentId]);

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    // Email validation
    if (!formData.email) {
      newErrors.email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'Email is invalid';
    } else if (!formData.email.toLowerCase().endsWith('@imocha.io')) {
      newErrors.email = 'Only @imocha.io email addresses are allowed';
    }

    // Password validation
    if (!formData.password) {
      newErrors.password = 'Password is required';
    } else if (isLogin) {
      // For login, just check if password is provided
      if (formData.password.length < 1) {
        newErrors.password = 'Password is required';
      }
    } else {
      // For signup, validate password strength to match backend requirements
      if (formData.password.length < 8) {
        newErrors.password = 'Password must be at least 8 characters long';
      } else if (formData.password.length > 128) {
        newErrors.password = 'Password must be less than 128 characters long';
      } else if (!/[a-z]/.test(formData.password)) {
        newErrors.password = 'Password must contain at least one lowercase letter';
      } else if (!/[A-Z]/.test(formData.password)) {
        newErrors.password = 'Password must contain at least one uppercase letter';
      } else if (!/\d/.test(formData.password)) {
        newErrors.password = 'Password must contain at least one number';
      } else if (!/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(formData.password)) {
        newErrors.password = 'Password must contain at least one special character';
      } else {
        // Check for common weak passwords
        const commonPasswords = [
          'password', 'password123', '123456', '123456789', 'qwerty',
          'abc123', 'password1', 'admin', 'letmein', 'welcome'
        ];
        if (commonPasswords.includes(formData.password.toLowerCase())) {
          newErrors.password = 'Password is too common and easily guessable';
        }
      }
    }

    // Signup-specific validations
    if (!isLogin) {
      if (!formData.firstName) {
        newErrors.firstName = 'First name is required';
      }

      if (!formData.lastName) {
        newErrors.lastName = 'Last name is required';
      }

      if (!formData.departmentId) {
        newErrors.departmentId = 'Department is required';
      }

      if (formData.password !== formData.confirmPassword) {
        newErrors.confirmPassword = 'Passwords do not match';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) return;

    try {
      let result;

      if (isLogin) {
        result = await login(formData.email, formData.password);
      } else {
        result = await signup(`${formData.firstName} ${formData.lastName}`, formData.email, formData.password, formData.departmentId);
      }

      if (!result.success) {
        const newErrors: FormErrors = {};

        if (result.errors) {
          // Map backend errors to form fields
          result.errors.forEach(error => {
            if (error.field) {
              // Map backend field names to frontend field names
              const fieldMap: { [key: string]: keyof FormErrors } = {
                'name': 'firstName',
                'email': 'email',
                'password': 'password',
                'department_id': 'departmentId'
              };

              const frontendField = fieldMap[error.field] || error.field as keyof FormErrors;
              if (frontendField in newErrors) {
                newErrors[frontendField] = error.message;
              } else {
                newErrors.general = error.message;
              }
            } else {
              newErrors.general = error.message;
            }
          });
        } else {
          newErrors.general = 'Authentication failed. Please try again.';
        }

        setErrors(newErrors);
      }
    } catch {
      setErrors({ general: 'An error occurred. Please try again.' });
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    // Clear error when user starts typing
    if (errors[name as keyof FormErrors]) {
      setErrors(prev => ({ ...prev, [name]: undefined }));
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-neutral-50 via-indigo-50 to-emerald-50 py-4 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full max-h-screen overflow-hidden">
        <Card className="shadow-2xl border-0 backdrop-blur-sm bg-white/95">
          <CardHeader className="text-center pt-2 pb-2">
            <CardTitle className="text-xl font-bold text-neutral-900 mb-1">
              Employee Dashboard
            </CardTitle>
            <p className="text-xs text-neutral-600">
              {isLogin ? 'Welcome back! Sign in to your account' : 'Join our team and create your account'}
            </p>
          </CardHeader>

          <CardContent className="pt-2">
            <form className="space-y-3" onSubmit={handleSubmit}>
              <div className="space-y-3">
                {!isLogin && (
                  <>
                    <Input
                      id="firstName"
                      name="firstName"
                      type="text"
                      label="First Name"
                      value={formData.firstName}
                      onChange={handleInputChange}
                      placeholder="Enter your first name"
                      error={errors.firstName}
                      icon={
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                        </svg>
                      }
                    />
                    <Input
                      id="lastName"
                      name="lastName"
                      type="text"
                      label="Last Name"
                      value={formData.lastName}
                      onChange={handleInputChange}
                      placeholder="Enter your last name"
                      error={errors.lastName}
                      icon={
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                        </svg>
                      }
                    />
                  </>
                )}

                <div className="space-y-1">
                  <Input
                    id="email"
                    name="email"
                    type="email"
                    label="Email Address"
                    autoComplete="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    placeholder={isLogin ? "Enter your email address" : "Enter your @imocha.io email"}
                    error={errors.email}
                    icon={
                      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207" />
                      </svg>
                    }
                  />
                  {!isLogin && (
                    <div className="text-xs text-gray-500 mt-1">
                      Only @imocha.io email addresses are allowed for signup
                    </div>
                  )}
                </div>

                <div className="space-y-1">
                  <Input
                    id="password"
                    name="password"
                    type="password"
                    label="Password"
                    autoComplete={isLogin ? "current-password" : "new-password"}
                    value={formData.password}
                    onChange={handleInputChange}
                    placeholder="Enter your password"
                    error={errors.password}
                    icon={
                      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                      </svg>
                    }
                  />
                  {!isLogin && (
                    <div className="text-xs text-gray-500 mt-1">
                      Password must be at least 8 characters with uppercase, lowercase, number, and special character
                    </div>
                  )}
                </div>

                {!isLogin && (
                  <>
                    <Input
                      id="confirmPassword"
                      name="confirmPassword"
                      type="password"
                      label="Confirm Password"
                      autoComplete="new-password"
                      value={formData.confirmPassword}
                      onChange={handleInputChange}
                      placeholder="Confirm your password"
                      error={errors.confirmPassword}
                      icon={
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                      }
                    />

                    <div className="space-y-2">
                      <label htmlFor="departmentId" className="block text-sm font-medium text-neutral-700">
                        Department
                      </label>
                      <select
                        id="departmentId"
                        name="departmentId"
                        value={formData.departmentId}
                        onChange={handleInputChange}
                        className="input"
                        disabled={isDepartmentsLoading || departments.length === 0}
                      >
                        {isDepartmentsLoading ? (
                          <option value="">Loading departments...</option>
                        ) : departments.length === 0 ? (
                          <option value="">No departments available</option>
                        ) : (
                          <>
                            <option value="">Select a department</option>
                            {departments.map(dept => (
                              <option key={dept.id} value={dept.id}>
                                {dept.name}
                              </option>
                            ))}
                          </>
                        )}
                      </select>
                      {errors.departmentId && (
                        <p className="text-sm text-red-600">{errors.departmentId}</p>
                      )}
                      {isDepartmentsLoading && (
                        <p className="text-sm text-blue-600">
                          Loading available departments...
                        </p>
                      )}
                      {!isDepartmentsLoading && departments.length === 0 && (
                        <p className="text-sm text-amber-600">
                          No departments are available for signup. Contact your administrator.
                        </p>
                      )}
                    </div>
                  </>
                )}
              </div>

              {errors.general && (
                <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
                  <p className="text-sm text-red-600 text-center">{errors.general}</p>
                </div>
              )}

              <div className="space-y-4">
                <Button
                  type="submit"
                  variant="primary"
                  size="lg"
                  loading={isLoading}
                  className="w-full"
                >
                  {isLogin ? 'Sign In' : 'Create Account'}
                </Button>

                <div className="text-center">
                  <Button
                    type="button"
                    variant="ghost"
                    onClick={() => {
                      setIsLogin(!isLogin);
                      setErrors({});
                      setFormData({
                        firstName: '',
                        lastName: '',
                        email: '',
                        password: '',
                        confirmPassword: '',
                        departmentId: departments.length > 0 ? departments[0].id : ''
                      });
                    }}
                    className="text-sm"
                  >
                    {isLogin ? "Don't have an account? Sign up" : "Already have an account? Sign in"}
                  </Button>
                </div>
              </div>
            </form>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default AuthForm;
