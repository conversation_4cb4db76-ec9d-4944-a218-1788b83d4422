import { API_BASE_URL } from '../config/api';

export interface CurrentTaskResponse {
  success: boolean;
  data: {
    id: string;
    task_description: string;
    status: 'active' | 'idle' | 'offline';
    priority: 'low' | 'medium' | 'high' | 'urgent';
    category: string;
    estimated_duration_minutes?: number;
    progress_percentage: number;
    project_id?: string;
    project_name?: string;
    expected_completion_date?: string;
    expected_finish_datetime?: string;
    blocking_issues?: string;
    number_of_questions?: number;
    tags: Array<{
      id: string;
      name: string;
      color?: string;
    }>;
    created_at: string;
    updated_at: string;
  } | null;
  message?: string;
}

/**
 * Fetch current task for a user
 */
export const fetchCurrentTask = async (userId?: string): Promise<CurrentTaskResponse | null> => {
  try {
    const token = localStorage.getItem('token');
    if (!token) {
      console.error('No authentication token found');
      return null;
    }

    const url = userId
      ? `${API_BASE_URL}/api/tasks/users/${userId}/current`
      : `${API_BASE_URL}/api/tasks/current`;

    console.log('Fetching current task:', {
      url,
      userId,
      hasToken: !!token,
      tokenPreview: token ? `${token.substring(0, 20)}...` : 'None'
    });

    const response = await fetch(url, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
    });

    console.log('Response status:', response.status);

    if (!response.ok) {
      console.error('Failed to fetch current task:', response.status);

      // Try to get error details
      try {
        const errorData = await response.json();
        console.error('Error response data:', errorData);
      } catch (e) {
        console.error('Could not parse error response');
      }

      // If unauthorized, clear the token and redirect to login
      if (response.status === 401) {
        console.error('Authentication failed - clearing stored token');
        localStorage.removeItem('token');
        localStorage.removeItem('user');
        // Optionally reload the page to trigger auth redirect
        window.location.reload();
      }

      return null;
    }

    const data: CurrentTaskResponse = await response.json();
    return data;
  } catch (error) {
    console.error('Error fetching current task:', error);
    return null;
  }
};

/**
 * Check if a task has expired based on expected_finish_datetime
 */
export const isTaskExpired = (expectedFinishDateTime?: string): boolean => {
  if (!expectedFinishDateTime) {
    return false;
  }

  const finishTime = new Date(expectedFinishDateTime);
  const now = new Date();
  
  return finishTime <= now;
};

/**
 * Get task status display text
 */
export const getTaskStatusText = (
  hasTask: boolean, 
  taskDescription?: string, 
  expectedFinishDateTime?: string
): string => {
  if (!hasTask) {
    return 'No current task';
  }

  if (isTaskExpired(expectedFinishDateTime)) {
    return 'Task completed (time expired)';
  }

  return taskDescription || 'Current task';
};

/**
 * Complete a task manually
 */
export const completeTask = async (taskId: string): Promise<boolean> => {
  try {
    const token = localStorage.getItem('token');
    if (!token) {
      console.error('No authentication token found');
      return false;
    }

    const response = await fetch(`${API_BASE_URL}/api/tasks/${taskId}/complete`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      console.error('Failed to complete task:', response.status);
      return false;
    }

    const data = await response.json();
    return data.success;
  } catch (error) {
    console.error('Error completing task:', error);
    return false;
  }
};

/**
 * Format time remaining until task deadline
 */
export const getTimeRemaining = (expectedFinishDateTime?: string): string | null => {
  if (!expectedFinishDateTime) {
    return null;
  }

  const finishTime = new Date(expectedFinishDateTime);
  const now = new Date();
  const diffMs = finishTime.getTime() - now.getTime();

  if (diffMs <= 0) {
    return 'Expired';
  }

  const diffMinutes = Math.floor(diffMs / (1000 * 60));
  const diffHours = Math.floor(diffMinutes / 60);
  const diffDays = Math.floor(diffHours / 24);

  if (diffDays > 0) {
    return `${diffDays} day${diffDays > 1 ? 's' : ''} remaining`;
  } else if (diffHours > 0) {
    return `${diffHours} hour${diffHours > 1 ? 's' : ''} remaining`;
  } else {
    return `${diffMinutes} minute${diffMinutes > 1 ? 's' : ''} remaining`;
  }
};
