// Production start script for Railway deployment
const path = require('path');
const fs = require('fs');

console.log('🚀 Starting Employee Task Dashboard Backend...');
console.log('📁 Working directory:', __dirname);
console.log('🌍 Environment:', process.env.NODE_ENV || 'development');
console.log('🔌 Port:', process.env.PORT || '3001');

// Check if dist directory exists
const distPath = path.join(__dirname, 'dist');
if (!fs.existsSync(distPath)) {
  console.error('❌ Error: dist directory not found at', distPath);
  console.error('📂 Available files:', fs.readdirSync(__dirname));
  process.exit(1);
}

// Check if server.js exists
const serverPath = path.join(distPath, 'server.js');
if (!fs.existsSync(serverPath)) {
  console.error('❌ Error: server.js not found at', serverPath);
  console.error('📂 Available files in dist:', fs.readdirSync(distPath));
  process.exit(1);
}

console.log('✅ Found server.js at', serverPath);

// Set up module resolution for @/ paths
const Module = require('module');
const originalResolveFilename = Module._resolveFilename;

Module._resolveFilename = function (request, parent, isMain) {
  if (request.startsWith('@/')) {
    // Handle @/ path resolution for compiled JavaScript
    const newRequest = request.replace('@/', path.join(__dirname, 'dist/'));
    try {
      return originalResolveFilename.call(this, newRequest, parent, isMain);
    } catch (error) {
      // Fallback: try with .js extension
      const withJs = newRequest.endsWith('.js') ? newRequest : newRequest + '.js';
      try {
        return originalResolveFilename.call(this, withJs, parent, isMain);
      } catch (jsError) {
        // If still fails, try index.js
        const withIndex = path.join(newRequest, 'index.js');
        return originalResolveFilename.call(this, withIndex, parent, isMain);
      }
    }
  }
  return originalResolveFilename.call(this, request, parent, isMain);
};

console.log('🔧 Module resolution configured');

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  console.error('❌ Uncaught Exception:', error);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

console.log('🎯 Starting server...');

// Start the server
try {
  require('./dist/server.js');
  console.log('✅ Server module loaded successfully');
} catch (error) {
  console.error('❌ Failed to start server:', error);
  process.exit(1);
}
