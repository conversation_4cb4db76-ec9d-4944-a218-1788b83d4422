import React from 'react';
import { cn } from '../../utils/cn';

export interface StatusIndicatorProps extends React.HTMLAttributes<HTMLDivElement> {
  status: 'active' | 'idle' | 'offline';
  showText?: boolean;
  size?: 'sm' | 'md' | 'lg';
}

const StatusIndicator = React.forwardRef<HTMLDivElement, StatusIndicatorProps>(
  ({ className, status, showText = true, size = 'md', ...props }, ref) => {
    const statusConfig = {
      active: {
        label: 'Active',
        dotClass: 'bg-success-500 animate-pulse',
        textClass: 'text-success-700',
      },
      idle: {
        label: 'Idle',
        dotClass: 'bg-warning-500',
        textClass: 'text-warning-700',
      },
      offline: {
        label: 'Offline',
        dotClass: 'bg-neutral-400',
        textClass: 'text-neutral-600',
      },
    };

    const sizeClasses = {
      sm: {
        dot: 'w-1.5 h-1.5',
        text: 'text-xs',
        spacing: 'mr-1.5',
      },
      md: {
        dot: 'w-2 h-2',
        text: 'text-sm',
        spacing: 'mr-2',
      },
      lg: {
        dot: 'w-2.5 h-2.5',
        text: 'text-base',
        spacing: 'mr-2.5',
      },
    };

    // Fallback to 'offline' if status is null, undefined, or not recognized
    const safeStatus = (status && statusConfig[status]) ? status : 'offline';
    const config = statusConfig[safeStatus];
    const sizeConfig = sizeClasses[size];

    return (
      <div
        ref={ref}
        className={cn('status-indicator inline-flex items-center', className)}
        {...props}
      >
        <span
          className={cn(
            'status-dot rounded-full',
            sizeConfig.dot,
            sizeConfig.spacing,
            config.dotClass
          )}
        />
        {showText && (
          <span className={cn(sizeConfig.text, config.textClass, 'font-medium')}>
            {config.label}
          </span>
        )}
      </div>
    );
  }
);

StatusIndicator.displayName = 'StatusIndicator';

export default StatusIndicator;
