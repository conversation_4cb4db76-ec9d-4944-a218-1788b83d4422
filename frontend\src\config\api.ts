// API Configuration
// This file handles the API base URL configuration for different environments

const getApiBaseUrl = (): string => {
  // Check if we have an environment variable for the API URL
  const envApiUrl = import.meta.env.VITE_API_URL;

  if (envApiUrl) {
    console.log('Using API URL from environment:', envApiUrl);
    return envApiUrl;
  }

  // Fallback logic for different environments
  if (import.meta.env.PROD) {
    console.log('Production mode detected, using fallback URL');
    // Default production fallback - Railway backend URL
    return 'https://hearty-endurance-production.up.railway.app';
  }

  // Development fallback
  console.log('Development mode detected, using localhost');
  return 'http://localhost:3001';
};

export const API_BASE_URL = getApiBaseUrl();

// Log the configuration for debugging
console.log('API Configuration:', {
  baseUrl: API_BASE_URL,
  environment: import.meta.env.MODE,
  isProduction: import.meta.env.PROD,
  envApiUrl: import.meta.env.VITE_API_URL,
});

// Helper function to create full API URLs
export const createApiUrl = (endpoint: string): string => {
  // Remove leading slash if present to avoid double slashes
  const cleanEndpoint = endpoint.startsWith('/') ? endpoint.slice(1) : endpoint;
  return `${API_BASE_URL}/${cleanEndpoint}`;
};

// Export for debugging
export const getApiConfig = () => ({
  baseUrl: API_BASE_URL,
  environment: import.meta.env.MODE,
  isProduction: import.meta.env.PROD,
  envApiUrl: import.meta.env.VITE_API_URL,
});
