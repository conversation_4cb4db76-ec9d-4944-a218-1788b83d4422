import { Request, Response } from 'express';
import { query } from '@/config/database';
import { logger } from '@/utils/logger';

// =====================================================
// DIAGNOSTICS CONTROLLERS
// =====================================================

// Database diagnostics endpoint
export const getDatabaseDiagnostics = async (req: Request, res: Response): Promise<void> => {
  try {
    const diagnostics: any = {
      timestamp: new Date().toISOString(),
      status: 'checking',
      tables: {},
      constraints: {},
      indexes: {},
      extensions: {}
    };

    // Check if core tables exist
    const tablesResult = await query(`
      SELECT table_name, 
             (SELECT COUNT(*) FROM information_schema.columns WHERE table_name = t.table_name) as column_count
      FROM information_schema.tables t
      WHERE table_schema = 'public' 
      AND table_name IN ('departments', 'users', 'task_updates', 'task_history', 'task_tags', 'task_update_tags', 'projects')
      ORDER BY table_name
    `);

    diagnostics.tables = tablesResult.rows.reduce((acc: any, row: any) => {
      acc[row.table_name] = {
        exists: true,
        column_count: parseInt(row.column_count)
      };
      return acc;
    }, {});

    // Check for missing core tables
    const expectedTables = ['departments', 'users', 'task_updates', 'task_history', 'task_tags', 'task_update_tags', 'projects'];
    expectedTables.forEach(tableName => {
      if (!diagnostics.tables[tableName]) {
        diagnostics.tables[tableName] = { exists: false, column_count: 0 };
      }
    });

    // Check specific columns in task_updates
    const taskUpdatesColumns = await query(`
      SELECT column_name, data_type, is_nullable, column_default
      FROM information_schema.columns
      WHERE table_name = 'task_updates'
      ORDER BY ordinal_position
    `);

    diagnostics.tables.task_updates.columns = taskUpdatesColumns.rows;

    // Check constraints
    const constraintsResult = await query(`
      SELECT constraint_name, constraint_type, table_name
      FROM information_schema.table_constraints
      WHERE table_schema = 'public'
      AND table_name IN ('task_updates', 'task_history', 'users', 'departments')
      ORDER BY table_name, constraint_name
    `);

    diagnostics.constraints = constraintsResult.rows;

    // Check indexes
    const indexesResult = await query(`
      SELECT indexname, tablename, indexdef
      FROM pg_indexes
      WHERE schemaname = 'public'
      AND tablename IN ('task_updates', 'task_history', 'users', 'departments')
      ORDER BY tablename, indexname
    `);

    diagnostics.indexes = indexesResult.rows;

    // Check extensions
    const extensionsResult = await query(`
      SELECT extname, extversion
      FROM pg_extension
      WHERE extname IN ('uuid-ossp', 'pg_stat_statements')
    `);

    diagnostics.extensions = extensionsResult.rows;

    // Check sample data
    try {
      const departmentCount = await query('SELECT COUNT(*) as count FROM departments');
      const userCount = await query('SELECT COUNT(*) as count FROM users');
      const taskCount = await query('SELECT COUNT(*) as count FROM task_updates');

      diagnostics.data = {
        departments: parseInt(departmentCount.rows[0].count),
        users: parseInt(userCount.rows[0].count),
        tasks: parseInt(taskCount.rows[0].count)
      };
    } catch (dataError) {
      diagnostics.data = {
        error: 'Failed to count records',
        details: dataError instanceof Error ? dataError.message : 'Unknown error'
      };
    }

    // Overall status
    const coreTablesExist = expectedTables.every(table => diagnostics.tables[table]?.exists);
    diagnostics.status = coreTablesExist ? 'healthy' : 'unhealthy';

    res.json({
      success: true,
      data: diagnostics
    });

  } catch (error) {
    logger.error('Database diagnostics error', {
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
      userId: req.user?.id
    });

    res.status(500).json({
      success: false,
      message: 'Failed to run database diagnostics',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Test specific endpoints
export const testTaskEndpoints = async (req: Request, res: Response): Promise<void> => {
  try {
    const tests: any = {
      timestamp: new Date().toISOString(),
      userId: req.user?.id,
      tests: {}
    };

    // Test 1: Check if user can fetch their own current task
    try {
      const currentTaskResult = await query(`
        SELECT 
          tu.*,
          p.name as project_name,
          array_agg(
            CASE WHEN tt.id IS NOT NULL THEN
              json_build_object('id', tt.id, 'name', tt.name, 'color', tt.color)
            END
          ) FILTER (WHERE tt.id IS NOT NULL) as tags
        FROM task_updates tu
        LEFT JOIN projects p ON tu.project_id = p.id
        LEFT JOIN task_update_tags tut ON tu.id = tut.task_update_id
        LEFT JOIN task_tags tt ON tut.task_tag_id = tt.id
        WHERE tu.user_id = $1
        GROUP BY tu.id, p.name, tu.updated_at
        ORDER BY tu.updated_at DESC
        LIMIT 1
      `, [req.user!.id]);

      tests.tests.currentTask = {
        status: 'success',
        hasTask: currentTaskResult.rows.length > 0,
        taskData: currentTaskResult.rows[0] || null
      };
    } catch (error) {
      tests.tests.currentTask = {
        status: 'error',
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }

    // Test 2: Check department access
    try {
      const departmentResult = await query(`
        SELECT d.id, d.name, COUNT(u.id) as employee_count
        FROM departments d
        LEFT JOIN users u ON d.id = u.department_id AND u.is_active = true
        WHERE d.id = $1 AND d.is_active = true
        GROUP BY d.id, d.name
      `, [req.user!.department_id]);

      tests.tests.department = {
        status: 'success',
        departmentExists: departmentResult.rows.length > 0,
        departmentData: departmentResult.rows[0] || null
      };
    } catch (error) {
      tests.tests.department = {
        status: 'error',
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }

    // Test 3: Check tag system
    try {
      const tagsResult = await query(`
        SELECT COUNT(*) as tag_count FROM task_tags
      `);

      const tagAssignmentsResult = await query(`
        SELECT COUNT(*) as assignment_count FROM task_update_tags
      `);

      tests.tests.tags = {
        status: 'success',
        tagCount: parseInt(tagsResult.rows[0].tag_count),
        assignmentCount: parseInt(tagAssignmentsResult.rows[0].assignment_count)
      };
    } catch (error) {
      tests.tests.tags = {
        status: 'error',
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }

    res.json({
      success: true,
      data: tests
    });

  } catch (error) {
    logger.error('Test endpoints error', {
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
      userId: req.user?.id
    });

    res.status(500).json({
      success: false,
      message: 'Failed to run endpoint tests',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};
