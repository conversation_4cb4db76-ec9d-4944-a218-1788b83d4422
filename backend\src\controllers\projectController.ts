import { Request, Response } from 'express';
import { query, transaction } from '@/config/database';
import { logger } from '@/utils/logger';
import { CreateProjectRequest, ProjectResponse } from '@/types/api';
import { CreateProjectData } from '@/types/database';

// =====================================================
// PROJECT CONTROLLERS
// =====================================================

// Get all projects
export const getProjects = async (req: Request, res: Response): Promise<void> => {
  try {
    const { department_id, status, search } = req.query;

    let whereClause = 'WHERE p.is_active = true';
    const params: any[] = [];
    let paramCount = 0;

    // Filter by department if specified
    if (department_id) {
      paramCount++;
      whereClause += ` AND p.department_id = $${paramCount}`;
      params.push(department_id);
    } else if (req.user!.role !== 'admin') {
      // Non-admin users can only see their department's projects
      paramCount++;
      whereClause += ` AND p.department_id = $${paramCount}`;
      params.push(req.user!.department_id);
    }

    // Filter by status if specified
    if (status) {
      paramCount++;
      whereClause += ` AND p.status = $${paramCount}`;
      params.push(status);
    }

    // Search filter
    if (search) {
      paramCount++;
      whereClause += ` AND (p.name ILIKE $${paramCount} OR p.description ILIKE $${paramCount} OR p.client_name ILIKE $${paramCount})`;
      params.push(`%${search}%`);
    }

    const result = await query(`
      SELECT 
        p.*,
        d.name as department_name,
        COUNT(tu.id) as active_tasks,
        COUNT(CASE WHEN th.action_type = 'completed' THEN 1 END) as completed_tasks,
        AVG(tu.progress_percentage) as progress_percentage
      FROM projects p
      LEFT JOIN departments d ON p.department_id = d.id
      LEFT JOIN task_updates tu ON p.id = tu.project_id
      LEFT JOIN task_history th ON p.id = th.project_id
      ${whereClause}
      GROUP BY p.id, d.name
      ORDER BY p.created_at DESC
    `, params);

    const projects = result.rows.map(row => ({
      id: row.id,
      name: row.name,
      description: row.description,
      client_name: row.client_name,
      department_id: row.department_id,
      department_name: row.department_name,
      start_date: row.start_date?.toISOString(),
      end_date: row.end_date?.toISOString(),
      status: row.status,
      priority: row.priority,
      budget: row.budget,
      active_tasks: parseInt(row.active_tasks) || 0,
      completed_tasks: parseInt(row.completed_tasks) || 0,
      progress_percentage: Math.round(parseFloat(row.progress_percentage) || 0),
      created_at: row.created_at.toISOString(),
      updated_at: row.updated_at.toISOString()
    }));

    res.json({
      success: true,
      data: projects
    });
  } catch (error) {
    logger.error('Get projects error', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

// Get project by ID
export const getProjectById = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;

    const result = await query(`
      SELECT 
        p.*,
        d.name as department_name,
        COUNT(tu.id) as active_tasks,
        COUNT(CASE WHEN th.action_type = 'completed' THEN 1 END) as completed_tasks,
        AVG(tu.progress_percentage) as progress_percentage
      FROM projects p
      LEFT JOIN departments d ON p.department_id = d.id
      LEFT JOIN task_updates tu ON p.id = tu.project_id
      LEFT JOIN task_history th ON p.id = th.project_id
      WHERE p.id = $1 AND p.is_active = true
      GROUP BY p.id, d.name
    `, [id]);

    if (result.rows.length === 0) {
      res.status(404).json({
        success: false,
        message: 'Project not found'
      });
      return;
    }

    const project = result.rows[0];

    // Check access permissions
    if (req.user!.role !== 'admin' && 
        project.department_id !== req.user!.department_id) {
      res.status(403).json({
        success: false,
        message: 'Access denied'
      });
      return;
    }

    const response: ProjectResponse = {
      id: project.id,
      name: project.name,
      description: project.description,
      client_name: project.client_name,
      department_id: project.department_id,
      department_name: project.department_name,
      start_date: project.start_date?.toISOString(),
      end_date: project.end_date?.toISOString(),
      status: project.status,
      priority: project.priority,
      budget: project.budget,
      active_tasks: parseInt(project.active_tasks) || 0,
      completed_tasks: parseInt(project.completed_tasks) || 0,
      progress_percentage: Math.round(parseFloat(project.progress_percentage) || 0),
      created_at: project.created_at.toISOString(),
      updated_at: project.updated_at.toISOString()
    };

    res.json({
      success: true,
      data: response
    });
  } catch (error) {
    logger.error('Get project by ID error', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

// Create new project
export const createProject = async (req: Request, res: Response): Promise<void> => {
  try {
    const projectData: CreateProjectRequest = req.body;

    // Only managers and admins can create projects
    if (req.user!.role === 'employee') {
      res.status(403).json({
        success: false,
        message: 'Insufficient permissions to create projects'
      });
      return;
    }

    // Validate department access
    if (projectData.department_id) {
      if (req.user!.role !== 'admin' && 
          projectData.department_id !== req.user!.department_id) {
        res.status(403).json({
          success: false,
          message: 'Cannot create project for different department'
        });
        return;
      }

      // Verify department exists
      const deptResult = await query(
        'SELECT id FROM departments WHERE id = $1 AND is_active = true',
        [projectData.department_id]
      );

      if (deptResult.rows.length === 0) {
        res.status(400).json({
          success: false,
          message: 'Invalid department ID'
        });
        return;
      }
    }

    // Validate dates
    if (projectData.start_date && projectData.end_date) {
      const startDate = new Date(projectData.start_date);
      const endDate = new Date(projectData.end_date);

      if (startDate >= endDate) {
        res.status(400).json({
          success: false,
          message: 'End date must be after start date'
        });
        return;
      }
    }

    const createData: CreateProjectData = {
      name: projectData.name,
      description: projectData.description,
      client_name: projectData.client_name,
      department_id: projectData.department_id || req.user!.department_id,
      start_date: projectData.start_date ? new Date(projectData.start_date) : undefined,
      end_date: projectData.end_date ? new Date(projectData.end_date) : undefined,
      priority: projectData.priority || 'medium',
      budget: projectData.budget
    };

    const result = await query(`
      INSERT INTO projects (
        name, description, client_name, department_id, start_date, 
        end_date, priority, budget, created_at, updated_at
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
      RETURNING *
    `, [
      createData.name,
      createData.description,
      createData.client_name,
      createData.department_id,
      createData.start_date,
      createData.end_date,
      createData.priority,
      createData.budget
    ]);

    const project = result.rows[0];

    // Get department name
    const deptResult = await query(
      'SELECT name FROM departments WHERE id = $1',
      [project.department_id]
    );

    const response: ProjectResponse = {
      id: project.id,
      name: project.name,
      description: project.description,
      client_name: project.client_name,
      department_id: project.department_id,
      department_name: deptResult.rows[0]?.name,
      start_date: project.start_date?.toISOString(),
      end_date: project.end_date?.toISOString(),
      status: project.status,
      priority: project.priority,
      budget: project.budget,
      active_tasks: 0,
      completed_tasks: 0,
      progress_percentage: 0,
      created_at: project.created_at.toISOString(),
      updated_at: project.updated_at.toISOString()
    };

    res.status(201).json({
      success: true,
      data: response,
      message: 'Project created successfully'
    });
  } catch (error) {
    logger.error('Create project error', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

// Update project
export const updateProject = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    const updateData = req.body;

    // Only managers and admins can update projects
    if (req.user!.role === 'employee') {
      res.status(403).json({
        success: false,
        message: 'Insufficient permissions to update projects'
      });
      return;
    }

    // Get current project
    const currentResult = await query(
      'SELECT * FROM projects WHERE id = $1 AND is_active = true',
      [id]
    );

    if (currentResult.rows.length === 0) {
      res.status(404).json({
        success: false,
        message: 'Project not found'
      });
      return;
    }

    const currentProject = currentResult.rows[0];

    // Check access permissions
    if (req.user!.role !== 'admin' && 
        currentProject.department_id !== req.user!.department_id) {
      res.status(403).json({
        success: false,
        message: 'Access denied'
      });
      return;
    }

    // Validate dates if provided
    if (updateData.start_date && updateData.end_date) {
      const startDate = new Date(updateData.start_date);
      const endDate = new Date(updateData.end_date);

      if (startDate >= endDate) {
        res.status(400).json({
          success: false,
          message: 'End date must be after start date'
        });
        return;
      }
    }

    // Build update query
    const updateFields = [];
    const params = [id];
    let paramCount = 1;

    const allowedFields = [
      'name', 'description', 'client_name', 'start_date', 
      'end_date', 'status', 'priority', 'budget'
    ];

    for (const field of allowedFields) {
      if (updateData[field] !== undefined) {
        paramCount++;
        updateFields.push(`${field} = $${paramCount}`);
        params.push(updateData[field]);
      }
    }

    if (updateFields.length === 0) {
      res.status(400).json({
        success: false,
        message: 'No valid fields to update'
      });
      return;
    }

    // Add updated_at
    paramCount++;
    updateFields.push(`updated_at = $${paramCount}`);
    params.push(new Date().toISOString());

    const result = await query(`
      UPDATE projects 
      SET ${updateFields.join(', ')}
      WHERE id = $1
      RETURNING *
    `, params);

    const project = result.rows[0];

    // Get department name and task counts
    const detailsResult = await query(`
      SELECT 
        d.name as department_name,
        COUNT(tu.id) as active_tasks,
        COUNT(CASE WHEN th.action_type = 'completed' THEN 1 END) as completed_tasks,
        AVG(tu.progress_percentage) as progress_percentage
      FROM projects p
      LEFT JOIN departments d ON p.department_id = d.id
      LEFT JOIN task_updates tu ON p.id = tu.project_id
      LEFT JOIN task_history th ON p.id = th.project_id
      WHERE p.id = $1
      GROUP BY d.name
    `, [id]);

    const details = detailsResult.rows[0] || {};

    const response: ProjectResponse = {
      id: project.id,
      name: project.name,
      description: project.description,
      client_name: project.client_name,
      department_id: project.department_id,
      department_name: details.department_name,
      start_date: project.start_date?.toISOString(),
      end_date: project.end_date?.toISOString(),
      status: project.status,
      priority: project.priority,
      budget: project.budget,
      active_tasks: parseInt(details.active_tasks) || 0,
      completed_tasks: parseInt(details.completed_tasks) || 0,
      progress_percentage: Math.round(parseFloat(details.progress_percentage) || 0),
      created_at: project.created_at.toISOString(),
      updated_at: project.updated_at.toISOString()
    };

    res.json({
      success: true,
      data: response,
      message: 'Project updated successfully'
    });
  } catch (error) {
    logger.error('Update project error', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};
