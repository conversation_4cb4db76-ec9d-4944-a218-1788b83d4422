// API request/response types

import { TaskStatus, TaskPriority, TaskCategory, UserRole } from './database';

// =====================================================
// AUTHENTICATION TYPES
// =====================================================

export interface LoginRequest {
  email: string;
  password: string;
}

export interface SignupRequest {
  name: string;
  email: string;
  password: string;
  department_id: string;
  phone?: string;
  timezone?: string;
}

export interface AuthResponse {
  user: {
    id: string;
    name: string;
    email: string;
    department_id: string;
    role: UserRole;
    timezone: string;
    avatar_url?: string;
  };
  token: string;
  refreshToken: string;
  expiresAt: string;
}

export interface RefreshTokenRequest {
  refreshToken: string;
}

export interface ChangePasswordRequest {
  currentPassword: string;
  newPassword: string;
}

// =====================================================
// TASK UPDATE TYPES
// =====================================================

export interface TaskUpdateRequest {
  task_description: string;
  status: TaskStatus;
  priority: TaskPriority;
  category: TaskCategory;
  estimated_duration_minutes?: number;
  progress_percentage: number;
  project_id?: string;
  expected_completion_date?: string;
  blocking_issues?: string;
  tags?: string[]; // Tag names
  number_of_questions?: number;
  expected_finish_datetime?: string;
}

export interface TaskUpdateResponse {
  id: string;
  task_description: string;
  status: TaskStatus;
  priority: TaskPriority;
  category: TaskCategory;
  estimated_duration_minutes?: number;
  progress_percentage: number;
  project_id?: string;
  project_name?: string;
  expected_completion_date?: string;
  expected_finish_datetime?: string;
  blocking_issues?: string;
  number_of_questions?: number;
  tags: Array<{
    id: string;
    name: string;
    color?: string;
  }>;
  created_at: string;
  updated_at: string;
}

// =====================================================
// DASHBOARD TYPES
// =====================================================

export interface DashboardResponse {
  departments: Array<{
    id: string;
    name: string;
    description?: string;
    total_employees: number;
    active_employees: number;
    idle_employees: number;
    offline_employees: number;
    urgent_tasks: number;
    blocked_tasks: number;
    activity_percentage: number;
  }>;
  summary: {
    total_employees: number;
    active_employees: number;
    total_departments: number;
    urgent_tasks: number;
    blocked_tasks: number;
  };
}

export interface DepartmentDetailResponse {
  department: {
    id: string;
    name: string;
    description?: string;
    manager_name?: string;
  };
  employees: Array<{
    id: string;
    name: string;
    email: string;
    task_description?: string;
    status?: TaskStatus;
    priority?: TaskPriority;
    category?: TaskCategory;
    progress_percentage?: number;
    project_name?: string;
    expected_completion_date?: string;
    blocking_issues?: string;
    last_updated?: string;
    minutes_since_update?: number;
    update_freshness: 'recent' | 'moderate' | 'stale';
  }>;
  projects: Array<{
    id: string;
    name: string;
    client_name?: string;
    status: string;
    priority: TaskPriority;
    active_tasks: number;
    progress: number;
  }>;
  metrics: {
    productivity_score: number;
    completion_rate: number;
    avg_task_duration: number;
    total_active_hours: number;
  };
}

// =====================================================
// ANALYTICS TYPES
// =====================================================

export interface ProductivityAnalyticsRequest {
  user_id?: string;
  department_id?: string;
  start_date: string;
  end_date: string;
  period: 'daily' | 'weekly' | 'monthly';
}

export interface ProductivityAnalyticsResponse {
  user_id?: string;
  user_name?: string;
  department_id?: string;
  department_name?: string;
  period: string;
  data: Array<{
    date: string;
    active_minutes: number;
    tasks_completed: number;
    productivity_score: number;
    status_updates: number;
  }>;
  summary: {
    total_active_minutes: number;
    total_tasks_completed: number;
    avg_productivity_score: number;
    trend: 'up' | 'down' | 'stable';
  };
}

export interface TaskCategoryAnalyticsResponse {
  categories: Array<{
    category: TaskCategory;
    total_tasks: number;
    completed_tasks: number;
    completion_rate: number;
    avg_duration_minutes: number;
    trend: 'up' | 'down' | 'stable';
  }>;
  period: {
    start_date: string;
    end_date: string;
  };
}

// =====================================================
// USER MANAGEMENT TYPES
// =====================================================

export interface UpdateProfileRequest {
  name?: string;
  phone?: string;
  timezone?: string;
  emergency_contact?: Record<string, any>;
  skills?: string[];
}

export interface UserProfileResponse {
  id: string;
  name: string;
  email: string;
  department_id: string;
  department_name: string;
  role: UserRole;
  hire_date?: string;
  timezone: string;
  avatar_url?: string;
  phone?: string;
  emergency_contact?: Record<string, any>;
  skills?: string[];
  last_login_at?: string;
  created_at: string;
}

// =====================================================
// PROJECT TYPES
// =====================================================

export interface CreateProjectRequest {
  name: string;
  description?: string;
  client_name?: string;
  department_id?: string;
  start_date?: string;
  end_date?: string;
  priority?: TaskPriority;
  budget?: number;
}

export interface ProjectResponse {
  id: string;
  name: string;
  description?: string;
  client_name?: string;
  department_id?: string;
  department_name?: string;
  start_date?: string;
  end_date?: string;
  status: string;
  priority: TaskPriority;
  budget?: number;
  active_tasks: number;
  completed_tasks: number;
  progress_percentage: number;
  created_at: string;
  updated_at: string;
}

// =====================================================
// COMMON API TYPES
// =====================================================

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  errors?: ValidationError[];
}

export interface ValidationError {
  field: string;
  message: string;
  code: string;
}

export interface PaginationQuery {
  page?: number;
  limit?: number;
  sort?: string;
  order?: 'asc' | 'desc';
  search?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

// =====================================================
// WEBSOCKET TYPES
// =====================================================

export interface WebSocketMessage {
  type: 'task_update' | 'user_status' | 'notification' | 'analytics_update';
  data: any;
  timestamp: string;
  user_id?: string;
  department_id?: string;
}

export interface TaskUpdateNotification {
  type: 'task_update';
  data: {
    user_id: string;
    user_name: string;
    department_id: string;
    task_description: string;
    status: TaskStatus;
    priority: TaskPriority;
    updated_at: string;
  };
}

export interface UserStatusNotification {
  type: 'user_status';
  data: {
    user_id: string;
    user_name: string;
    status: TaskStatus;
    last_seen: string;
  };
}

// =====================================================
// ERROR TYPES
// =====================================================

export interface ApiError {
  code: string;
  message: string;
  details?: Record<string, any>;
  statusCode: number;
}

export interface DatabaseError extends Error {
  code?: string;
  detail?: string;
  constraint?: string;
}

// =====================================================
// HEALTH CHECK TYPES
// =====================================================

export interface HealthCheckResponse {
  status: 'healthy' | 'unhealthy';
  timestamp: string;
  uptime: number;
  services: {
    database: {
      status: 'healthy' | 'unhealthy';
      responseTime?: number;
      details?: any;
    };
    redis?: {
      status: 'healthy' | 'unhealthy';
      responseTime?: number;
    };
  };
  version: string;
}
